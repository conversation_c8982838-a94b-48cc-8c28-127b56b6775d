import { Segments, Jo<PERSON>, celebrate } from "celebrate";
export default {
  addBank: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        bank_name: Joi.string().required(),
        bank_account_number: Joi.string().required().pattern(/^[0-9]{8}$/).message('Please enter valid account number.'),
        bank_status  : Joi.string().required(),
        bank_sort_code :  Joi.string().required().pattern(/^\d{2}-\d{2}-\d{2}$/).message('Please enter valid sort number.'),
        branch_id : Joi.number().required()
      }),
    }),
    updateBank: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        bank_name: Joi.string().required(),
        bank_account_number: Joi.string().required().pattern(/^[0-9]{8}$/).message('Please enter valid account number.'),
        bank_status  : Joi.string().required(),
        bank_sort_code :  Joi.string().required().pattern(/^\d{2}-\d{2}-\d{2}$/).message('Please enter valid sort number.')
      }),
    }),
};
