import { Segments, Jo<PERSON>, celebrate } from "celebrate";
export default {
  addCard: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        card_name: Joi.string().required(),
        card_number: Joi.string().required().max(30).message('Please enter valid card number.'),
        card_status : Joi.string().required(),
        branch_id : Joi.number().required()
      }),
    }),
    updateCard: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        card_name: Joi.string().required(),
        card_number: Joi.string().required().max(30).message('Please enter valid card number.'),
        card_status : Joi.string().required()
      }),
    }),
};
