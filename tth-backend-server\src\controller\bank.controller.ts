import { Request, Response } from "express";
import { Branch, branch_status } from "../models/Branch";
import { Op } from "sequelize";
import {  permittedForAdmin } from "../helper/common";
import { StatusCodes } from "http-status-codes";
import {  getPaginatedItems, getPagination } from "../helper/utils";
import { ROLE_CONSTANT } from "../helper/constant";
import { Bank , bank_status as bankStatus } from "../models/Bank";
/**
 *  Create new bank
 * @param req
 * @param res
 * @returns
 */

const addBank = async (req: Request, res: Response) => {
  try {
    const { bank_name = "", bank_account_number = "", bank_status , bank_sort_code , branch_id } = req.body;
    const checkPermission = await permittedForAdmin(req.user?.id, [
      ROLE_CONSTANT.SUPER_ADMIN,
      ROLE_CONSTANT.ADMIN,
      ROLE_CONSTANT.DIRECTOR,
      ROLE_CONSTANT.ACCOUNTANT,
    ]);
    if (!checkPermission)
      return res
        .status(StatusCodes.FORBIDDEN)
        .json({ status: false, message: res.__("PERMISSION_DENIED") });

    const findBranch = await Branch.findOne({where:{id : branch_id, branch_status : {[Op.not] :branch_status.DELETED}}})   
    if(!findBranch){
      return res
      .status(StatusCodes.BAD_REQUEST)
      .json({ status: false, message: res.__("BRANCH_NOT_FOUND") });
    } 
    const findBank = await Bank.findOne({
      where: {
        bank_account_number : bank_account_number,
        branch_id : findBranch.id,
        bank_status: { [Op.not]: bankStatus.DELETED },
      },
    });
    
    if (findBank) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("BANK_ALREADY_EXIST") });
    } else {
      const findBankByBranch = await Bank.findOne({
        where: {
          bank_status: { [Op.not]: bankStatus.DELETED },
          branch_id : findBranch.id
        },order :[['bank_order','DESC']]
      });
      const addBank: any = await Bank.setHeaders(req).create({
        bank_account_number,
        bank_name,
        bank_status: bank_status,
        bank_sort_code,
        branch_id,
        bank_order :findBankByBranch ? findBankByBranch.bank_order + 1 : 1,
        created_by: req.user.id,
        updated_by: req.user.id,
      } as any);
      if (addBank) {
        return res.status(StatusCodes.CREATED).json({
          status: true,
          message: res.__("SUCCESS_BANK_ADDED"),
          data: addBank,
        });
      } else {
        return res
          .status(StatusCodes.BAD_REQUEST)
          .json({ status: false, message: res.__("FAIL_BANK_ADDING") });
      }
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Update bank
 * @param req
 * @param res
 * @returns
 */

const updateBank = async (req: Request, res: Response) => {
  try {
    const { bank_name = "", bank_account_number = "", bank_status , bank_sort_code } = req.body;
    const { bank_id } = req.params;
    const checkPermission = await permittedForAdmin(req.user?.id, [
      ROLE_CONSTANT.SUPER_ADMIN,
      ROLE_CONSTANT.ADMIN,
      ROLE_CONSTANT.DIRECTOR,
      ROLE_CONSTANT.ACCOUNTANT,
    ]);
    if (!checkPermission)
      return res
        .status(StatusCodes.FORBIDDEN)
        .json({ status: false, message: res.__("PERMISSION_DENIED") });
    const findBank = await Bank.findOne({
      where: {
        id : bank_id,
        bank_status: { [Op.not]: bankStatus.DELETED },
      },
    });
    if (!findBank) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("BANK_NOT_FOUND") });
    } else {
      const updateBank: any = await Bank.setHeaders(req).update({
        bank_name,
        bank_account_number,
        bank_status,
        bank_sort_code,
        branch_id : findBank?.branch_id,
        bank_order : findBank?.bank_order,
        updated_by: req.user.id,
      },{where:{id : findBank.id}});
      if (updateBank.length > 0) {
        return res.status(StatusCodes.CREATED).json({
          status: true,
          message: res.__("SUCCESS_BANK_UPDATED"),
        });
      } else {
        return res
          .status(StatusCodes.BAD_REQUEST)
          .json({ status: false, message: res.__("FAIL_BANK_UPDATING") });
      }
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Get All bank list
 * @param req
 * @param res
 * @returns
 */

const getAlLBank = async (req: Request, res: Response) => {
  try {
     const {page ,size,bank_status ,search }: any = req.query
     const { limit, offset } = getPagination(page, size);
      const bankObj : any = {
        where :{bank_status :{[Op.not] : bankStatus.DELETED}}
      }
      if(search){
        bankObj.where.bank_name = { [Op.like]: `%${search}%` }
      }
      if (page && size) {
        bankObj.limit = Number(limit);
        bankObj.offset = Number(offset);
      }
      if(bank_status){
        bankObj.where.bank_status = bank_status
      }
      const { count ,rows: bankList} = await Bank.setHeaders(req).findAndCountAll(bankObj);
      const { total_pages } = getPaginatedItems(
        size,
        page,
        count || 0,
      );
      return res.status(StatusCodes.OK).json({
        status: true,
        data: bankList,
        message: res.__("SUCCESS_FETCHED"),
        count: count,
        page: parseInt(page),
        size: parseInt(size),
        total_pages,
      });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Delete  by id
 * @param req
 * @param res
 * @returns
 */

const deleteBank = async (req: Request, res: Response) => {
  try {
    const { bank_id } = req.params;
    const getBank = await Bank.findOne({ where:{id : bank_id , bank_status : {[Op.not]: bankStatus.DELETED}}});
    if (!getBank) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("BANK_NOT_FOUND")
      });
    } 
    const deleteBank = await Bank.update({ bank_status: bankStatus.DELETED }, { where: {id: getBank.id}})
    if(deleteBank.length > 0){
      return res.status(StatusCodes.OK).json({status : true , message : res.__("SUCCESS_BANK_DELETED")})
    }else{
      return res.status(StatusCodes.BAD_REQUEST).json({status : false , message : res.__("FAIL_BANK_DELETING")})
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Get bank by id
 * @param req
 * @param res
 * @returns
 */

const getBankById = async (req: Request, res: Response) => {
  try {
    const { bank_id } = req.params;
    const getBank = await Bank.findOne({ where:{id : bank_id , bank_status : {[Op.not]: bankStatus.DELETED}} });
    if (!getBank) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        data: {},
        message: res.__("BANK_NOT_FOUND")
      });
    }else{
      return res.status(StatusCodes.OK).json({
        status: true,
        data: getBank || {},
        message: res.__("SUCCESS_FETCHED")
      });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const getBankByBranch = async (req: Request, res: Response) => {
  try {
    const { branch_id } = req.params;
    const {page ,size,bank_status ,search }: any = req.query
    const { limit, offset } = getPagination(page, size);
    const findBranch = await Branch.findOne({where:{id : branch_id, branch_status : {[Op.not] :branch_status.DELETED}}})   
    if(!findBranch){
        return res
        .status(StatusCodes.BAD_REQUEST)
        .json({ status: false, message: res.__("BRANCH_NOT_FOUND") });
    }
    const bankObj : any = {
      where :{bank_status :{[Op.not] : bankStatus.DELETED}, branch_id  :branch_id},
      order : [['bank_order','ASC']]
    }
    if(search){
      bankObj.where.bank_name = { [Op.like]: `%${search}%` }
    }
    if (page && size) {
      bankObj.limit = Number(limit);
      bankObj.offset = Number(offset);
    }
    if(bank_status){
      bankObj.where.bank_status = bank_status
    }
    const { count,rows :getBankByBranch} = await Bank.findAndCountAll(bankObj);
    const { total_pages } = getPaginatedItems(
      size,
      page,
      count || 0,
    );
    return res.status(StatusCodes.OK).json({
      status: true,
      data: getBankByBranch,
      message: res.__("SUCCESS_FETCHED"),
      count: count,
      page: parseInt(page),
      size: parseInt(size),
      total_pages,
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const updateBankOrder = async (req: any, res: any) => {
  try {
    const { bank_list } = req.body;
    const { branch_id } = req.params;

    const findBranch = await Branch.findOne({
      where: {
        id: branch_id,
        branch_status: { [Op.not]: branch_status.DELETED },
      },
    });
    if (!findBranch) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("BRANCH_NOT_FOUND") });
    }

    for (let i = 0; bank_list.length > i; i++) {
      const findBank = await Bank.findOne({
        where: { id: bank_list[i] },
      });

      if (findBank) {
        await Bank.setHeaders(req).update(
          {
            bank_order : i + 1,
            updated_by: req.user.id,
          },
          {
            where: {
              branch_id: branch_id,
              id : bank_list[i],
            },
          },
        );
      }
    }
    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_BANK_ORDER_UPDATED"),
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};


export default {
  addBank,
  updateBank,
  getAlLBank,
  deleteBank,
  getBankById,
  getBankByBranch,
  updateBankOrder
};
