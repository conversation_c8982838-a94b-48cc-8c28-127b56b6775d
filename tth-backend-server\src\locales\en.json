{"BRANCH_CREATION_SUCCESSED": "branch created successfully.", "BRANCH_CREATION_FAILED": "branch not created.", "BRANCH_UPDATION_SUCCESSED": "branch updated successfully.", "BRANCH_UPDATION_FAILED": "branch not updated.", "BRANCH_NOT_FOUND": "branch not found.", "BRANCH_DELETION_FAILED": "branch deleted Failed.", "BRANCH_DELETATION_SUCCESSED": "branch deleted Successfully.", "BRANCH_NAME_EXIST": "This branch Name is already exists.", "DEPARTMENT_NAME_EXIST": "This department Name is already exists.", "DEPARTMENT_CREATION_SUCCESSED": "department created successfully.", "DEPARTMENT_CREATION_FAILED": "department not created.", "DEPARTMENT_NOT_FOUND": "department not found.", "DEPARTMENT_UPDATION_SUCCESSED": "department updated successfully.", "DEPARTMENT_UPDATION_FAILED": "department not updated.", "DEPARTMENT_DELETATION_FAILED": "department deleted Failed.", "DEPARTMENT_DELETATION_SUCCESSED": "department deleted Successfully.", "PERMISSION_DENIED": "You don't have permission.", "USER_CREATION_SUCCESSED": "Staff created successfully.", "USER_CREATION_FAILED": " User creation Failed.", "ERROR_INCORRECT_EMAIL_PASSWORD": "Incorrect email or password entered, please try again.", "ERROR_INCORRECT_PIN": "Incorrect PIN entered, please try again.", "SUCCESS_LOGIN": "Login Successfully.", "SOMETHING_WENT_WRONG": "Something went wrong.", "ERR_TOKEN_NOT_FOUND": "Token not found.Please log in again. ", "ERROR_USER_NOT_FOUND": "User does not exist!", "SUCCESS_FORGOT_OTP": "Password reset otp has been sent to your email.", "USER_ALREADY_EXIST": "Email address is already in use. Please try another one.", "USER_UPDATION_SUCCESSED": "User details updated successfully.", "USER_UPDATION_FAILED": "User details could not be updated.", "OLD_PASSWORD_WRONG": "Old Password is incorrect.", "RESET_PASSWORD_SUCCESSFULL": "Password Reset Successfully.", "RESET_PASSWORD_FAILED": "Reset Password failed.", "PIN_ALREADY_SET": "Your pin has already set.", "PIN_AND_CONFIRM_PIN_NOT_MATCH": "New Pin and confirm <PERSON><PERSON> should match.", "LOGIN_PIN_SUCCESSFULL": "Log in pin generated successfully.", "LOGIN_PIN_FAILED": "Log in pin generation failed.", "OLD_PIN_AND_NEW_PIN_match": "Both old pin and new pin should be different.", "RESET_PIN_SUCCESSFUL": "<PERSON><PERSON>set Successfully.", "RESET_PIN_FAILED": "<PERSON><PERSON> failed.", "SET_PIN_FIRST": "Please set the pin first.", "USER_PROFILE_UPDATED": "Profile Updated Successfully.", "USER_PROFILE_NOT_UPDATED": "Profile Could Not Be Updated.", "SUCCESS_FETCHED": "Data fetched successfully.", "USER_DELETED_SUCCESSFULL": "User Deleted Successfully.", "USER_DELETION_FAILED": "Users are not deleted.", "PASSWORD_WRONG": "The provided password is incorrect.", "BRANCH_REQUIRED": "Please select branch. ", "DEPARTMENT_REQUIRED": "Please select department.", "ROLE_SWITCHED_SUCCESSFULLY": "Role switched successfully.", "ROLE_NOT_SWITCHED": "Failed to switch role.", "ROLE_NOT_FOUND": "No such Role Found.", "FORM_CREATED": "Form Created Successfully.", "FORM_ALREADY_EXIST": " This form is already exist.", "SUCCESS_FORM_CREATED": "Form field added successfully.", "FAIL_FORM_ALREADY_EXIST": "All information is already filled out.", "FAIL_DATA_NOT_FOUND": "No data found.", "FAIL_FORM_NOT_FOUND": "This form is not available.", "FAIL_CHECKLIST_NOT_FOUND": "Checklist not found.", "SUCCESS_REJECT_FORM_VERIFICATION": "Form rejected.", "SUCCESS_ACCEPT_FORM_VERIFIED": "CheckList saved successfully.", "FAILED_FORM_VERIFICATION_PROCESS": "Verification process failed.", "FAIL_USER_ONBORDING_NOT_COMPLTED": "Onboarding Process is not completed yet.", "FAIL_PLEASE_VERIFY_ALL_CHECKLIST": "Please verify all checklist.", "SUCCESS_CATEGORY_CREATED": "Success category created.", "SUCCESS_CATEGORY_UPDATED": "Success category updated.", "FAIL_DATA_FETCHED": "Data not found.", "SUCCESS_MEDIA_UPLOADED": "Media created successfully.", "SUCCESS_MEDIA_UPDATE_UPLOADED": "Media updated successfully.", "FAIL_MEDIA_UPLOADATION": "Media updated failed.", "FILE_NOT_FOUND": "File not found.", "FAIL_NEW_PASSWORD_MUST_DIFFERENT": "New password must be different.", "OTP_EXPIRE": "The OTP has expired. Please request a new one.", "OTP_NOT_MATCH": "The OTP you entered is invalid. Please try again.", "SUCCESS_RESET_PASSWORD": "Your password has been successfully reset.", "FAIL_LEAVE_LIMIT_EXCEED": "Maximum leave limit reached for the year", "SUCCESS_LEAVE_APPLIED": "Leave application submitted successfully", "FAIL_LEAVE_APPLY_PROCESS": "Leave apply process failed.", "SUCCESS_RESET_PIN": "Password reset successfully.", "FAIL_REQUEST_NOT_FOUND": "Request not found.", "SUCCESS_USER_REQUEST_UPDATE": "User request updated successfully", "FAIL_USER_REQUEST_NOT_UPDATE": "User request updating failed.", "SUCCESS_OTP_SENT": "OTP sent successfully.", "FAIL_OTP_RESEND": "Failed to send OTP.", "SUCCESS_OTP_VERIFIED": "OTP verified successfully.", "FAIL_PLEASE_SET_PIN": "You don't have any pin.", "FAIL_LEAVE_DATA_NOT_FOUND": "Leave data not found.", "SUCCESS_LEAVE_DATA_FETCHED": "Leave list successfully fetched.", "SUCCESS_LOGOUT": "You have successfully logged out.", "FAIL_LOGOUT": "There was an issue logging you out. Please try again.", "CATEGORY_EXIST": "A folder with the same name already exists.", "FAIL_PLAYLIST_NOT_ACTIVE": "Playlist not active.", "FAIL_MEDIA_NOT_ACTIVE": "Media not active.", "FAIL_DATA_FETCHING": "Data not found.", "FAIL_SETTING_NOT_FOUND": "Setting not found.", "CATEGORY_NOT_FOUND": "Category not found.", "PLAYLIST_NOT_FOUND": "Playlist not found.", "SUCCESS_FORM_UPDATED": "Form detail updated successfully.", "SUCCESS_PLAYLIST_MEDIA_TRACK_UPDATED": "Playlist track successfully.", "ERROR_PLEASE_VERIFY_ACCOUNT": "Please verify your account with given credential.", "SUCCESS_PLAYLIST_MEDIA_UPDATED": "Playlist media updated successfully.", "SUCCESS_FORM_REQUEST_SENDED": "Form request send successfully.", "SUCCESS_FILE_DELETED": "File deleted successfully.", "SUCCESS_FILE_INACTIVATED": "File deactivated successfully.", "SUCCESS_FILE_ACTIVATED": "File activated successfully.", "ERROR_ONBORDING_PENDING": "Your onboarding process pending.", "FAIL_ONLY_ONE_TIME_UPDATE_PROFILE": "You can update your profile only one time.", "PLAYLIST_ALREADY_EXIST": "Playlist already exist.", "SUCCESS_PLAYLIST_UPDATING": "Playlist updated successfully.", "SUCCESS_MEDIA_ADDED_INTO_PLAYLIST": "Media added into playlist successfully.", "SUCCESS_MEDIA_REMOVED_FROM_PLAYLIST": "Media removed successfully.", "FAIL_MEDIA_REMOVED_FROM_PLAYLIST": "Failed to remove media.", "FAIL_MEDIA_NOT_FOUND_IN_PLAYLIST": "Media not exist in playlist", "SUCCESS_SETTING_ADDED": "Setting added successfully.", "SUCCESS_SETTING_UPDATED": "Setting updated successfully.", "SUCCESS_SETTING_LIST_FETCHED": "Set<PERSON> fetched successfully.", "SUCCESS_NOTIFICATION_READING": "Notification marked as read.", "FAIL_CATEGORY_UPDATION": "Failed to update the category.", "FAIL_PLAYLIST_ALREADY_EXIST": "The playlist already exists", "SUCCESS_CATEGORY_DELETED": "The category has been successfully deleted.", "FAIL_CATEGORY_DELETATION": "Failed to delete the category.", "FAIL_PLAYLIST_CREATION": "Failed to create the playlist.", "SUCCESS_PLAYLIST_CREATED": "The playlist has been successfully created.", "FAIL_PLAYLIST_UPDATING": "Failed to update the playlist.", "FAIL_MEDIA_ALREADY_EXIST": "The media item already exists.", "SUCCESS_PLAYLIST_DELETED": "The playlist has been successfully deleted.", "FAIL_PLAYLIST_DELETATION": "Failed to delete the playlist.", "MEDIA_NOT_FOUND": "Media not found", "FAIL_CATEGORY_NOT_ACTIVE": "Failed to perform the action. The category is not currently active.", "FAIL_PLAYLIST_ALREADY_IN_CATEGORY": "Failed to add playlist. The playlist is already in the category.", "SUCCESS_PLAYLIST_ADDED_INTO_CATEGORY": "The playlist has been successfully added to the category.", "FAIL_MEDIA_NOT_ADD_INTO_PLAYLIST": "Failed to add media to the playlist.", "ERROR_PLAYLIST_NOT_FOUND": "Playlist not found.", "FAIL_NOTIFICATION_READING": "Failed to read the notification. Please try again later.", "SUCCESS_MEDIA_DELETED": "The media has been successfully deleted.", "FAIL_MEDIA_DELETATION": "Failed to delete the media.", "FAIL_RESIGNATION_REQUEST_EXIST": "Resignation request already exists.", "SUCCESS_RESIGNATION_SENDED": "Resignation sent successfully.", "FAIL_RESIGNATION_NOT_SEND": "Failed to sent resignation.", "SUCCESS_FORM_VERIFIED": "Form verified successfully.", "SUCCESS_NOTIFICATION_TOKEN_UPDATED": "Notification Token updated.", "FAIL_TOKEN_UPDATATION": "Notification Token update failed.", "SUCCESS_NOTIFICATION_SENT": "Notification sent successfully.", "SUCCESS_RESIGNATION_UPDATED": "Resignation updated successfully.", "SUCCESS_USER_PASSWORD_UPDATED": "User password updated.", "FAIL_LEAVE_ALREADY_PENDING": "Leave request already pending.", "FAIL_TOKEN_EXPIRED": "Session is expired. Please log in again.", "USER_NOT_EXIST": "User detail not found.", "SUCCESS_USER_RESET": "User reset successfully.", "FAIL_USER_RESET": "Failed to reset user.", "SUCCESS_FORGOT_OTP_PIN": "Pin reset otp has been sent to your email.", "ERROR_NEW_PIN_MUST_DIFFERENT": "New pin must be different.", "FILE_NOT_EXIST": "File not exist", "SUCCESSFULLY_BRANCH_SETTING_ADDED": "Branch setting added successfully.", "FAIL_BRANCH_SETTING": "Failed to add branch setting.", "FAIL_BRANCH_NOT_ACTIVE": "Branch not active.", "FAIL_HEALTH_SAFETY_CATEGORY_NOT_ACTIVE": "Health safety category not found.", "SUCCESS_PLAYLIST_ADDED_INTO_HEALTH": "Playlist added successfully.", "CARD_ALREADY_EXIST": "Card already exist.", "SUCCESS_CARD_ADDED": "Card added successfully.", "FAIL_CARD_ADDING": "Failed to add card.", "SUCCESS_CARD_UPDATED": "Card updated successfully.", "FAIL_CARD_UPDATING": "Failed to update card.", "CARD_NOT_FOUND": "Card not found.", "SUCCESS_CARD_DELETED": "Card deleted successfully.", "FAIL_CARD_DELETING": "Failed to delete card.", "BANK_ALREADY_EXIST": "Bank already exist.", "SUCCESS_BANK_ADDED": "Bank added successfully.", "FAIL_BANK_ADDING": "Failed to add bank.", "SUCCESS_BANK_UPDATED": "Bank updated successfully.", "FAIL_BANK_UPDATING": "Failed to update bank.", "BANK_NOT_FOUND": "Bank not found.", "SUCCESS_BANK_DELETED": "Bank deleted successfully.", "FAIL_BANK_DELETING": "Failed to delete bank.", "SELECT_RECIPIENT": "Select a recipient category for sending notifications", "SUCCESS_CARD_ORDER_UPDATED": "Card order updated successfully.", "SUCCESS_BANK_ORDER_UPDATED": "Bank order updated successfully.", "FAIL_CANNOT_ADD_AFTER": "Cannot add DSR after 5:30 PM today for yesterday.", "FAIL_CANNOT_ADD_BEFORE": "DSR date must be today or yesterday.", "DSR_ALREADY_ADDED": "DSR already exist.", "DSR_ADDED_SUCCESSFULLY": "DSR added successfully.", "FAIL_TO_ADD_DSR": "Failed to add Dsr.", "FAIL_DSR_NOT_FOUND": "DSR not found.", "DSR_UPDATED_SUCCESSFULLY": "DSR updated successfully.", "DSR_DELETED_SUCCESSFULLY": "DSR deleted successfully.", "FAILED_TO_DELETE_DSR": "Failed to delete DSR.", "DSR_UPDATED_REQUEST_EXIST": "DSR update request already exist.", "DSR_REQUEST_ADDED": "DSR update request sent successfully.", "FAIL_TO_ADD_DSR_REQUEST": "Failed to send DSR update request.", "FAIL_DSR_REQUEST_NOT_FOUND": "DSR request not found.", "DSR_REQUEST_APPROVED": "DSR request approved.", "DSR_REQUEST_REJECTED": "DSR request rejected.", "FAIL_CANNOT_ADD_AFTER_TODAY_TIME": "Cannot add DSR before 8 AM for today.", "FAIL_USER_SIGNATURE_MISSING": "You must need to add a signature before creating employment contract.", "CHANGE_REQUEST_SEND_SUCCESSFULLY": "Change request send successfully.", "CHANGE_REQUEST_SEND_FAILED": "Failed to send change request.", "CHANGE_REQUEST_APPROVED": "Change request approved.", "FAIL_CHANGE_REQUEST_APPROVED": "Failed to update change request", "CHANGE_REQUEST_REJECTED": "Change request rejected.", "FAIL_CHANGE_REQUEST_NOT_FOUND": "Change request not found.", "REQUEST_STATUS_UPDATED": "Change request status updated successfully.", "CHANGE_REQUEST_DELETED": "Change request deleted successfully.", "FAIL_CHANGE_REQUEST_DELETED": "Failed to delete Change request.", "CONTRACT_GENERATED_SUCCESSFULLY": "Employment contract generated successfully.", "CONTRACT_NOT_FOUND": "Employment contract not found.", "USER_INVITED_SUCCESSFULLY": "User invited successfully.", "SELECTED_USER_INVITED": "Selected users invited successfully.", "USER_INVITE_FAIL": "User invitation failed.", "USER_PASSOWORD_RESET_RE_LOGIN": "Your password has been reset. Please log in again to continue.", "WAIT_BEFORE_RESEND": "Please wait 3 minutes before resending your request.", "TOO_MANY_ATTEMPTS_TODAY": "You have reached the limit of attempts for today. Please try again tomorrow.", "CONTACT_TO_ADMIN": "Please contact to admin for further process.", "RIGHT_TO_WORK_REQUIRED": "Photo ID, NI Letter, and Address Proof (Utility Bill/Council Tax bill/Bank Statement) are required.", "NO_RECORDS_FOUND": "Data not found.", "PLEASE_SELECT_ANY_FORM": "Please selecte any form.", "USER_PIN_RESET_RE_LOGIN": "Your pin has been reset. Please log in again to continue.", "CAN'T_ADD_FOLDER_MORE_THAN_ONE_IN_TRANING": "Cannot add more than one folder in training.", "UPLOADED": "Uploaded successfully.", "ERROR_UPLOAD_FILE": "Error uploading file.", "PARENT_CATEGORY_NOT_FOUND": "Parent category not found.", "CANNOT_ADD_SUB_IN_TRANING": "Cannot add a sub-category in training.", "Category_NOT_FOUND": "Category not found.", "SUCCESSFULL_CATEGORY_UPDATE": "Category updated successfully.", "SUCCESS_TEMPLATE_CREATED": "Template created successfully.", "SUCCESS_TEMPLATE_COPIED": "Template copied successfully.", "SUCCESS_TEMPLATE_UPDATED": "Template updated successfully.", "DELETE_TEMPLATE_FAIL": "Cannot delete {{templateName}} as it is currently in use.", "PARENT_REQUIRED_FOR_CATEGORY_ITEM": "Parent category is required for this item.", "ITEM_UPLOAD_REQUIRED_FOR_FILE_TYPE": "Item upload is required for file type categories.", "ITEM_UPLOAD_NOT_ALLOWED_IN_FOLDER": "Item upload is not allowed during category creation.", "CANNOT_CREATE_FOLDER_BEYOND_10_LEVELS": "Cannot create folders beyond 10 levels.", "SUCCESS_SEND_REMINDER": "<PERSON><PERSON><PERSON> sent successfully.", "SUCCESS_SEND_REMINDER_REGENERATE": "Employment contract generated and reminder sent successfully", "CATEGORY_ITEM_ORDER_UPDATED": "Category item order updated successfully.", "ITEM_NOT_FOUND": "Item not found.", "CAN'T_MOVE_PARENT_FOLDER": "Cannot move a main category.", "MOVE_DESTINATION_NOT_FOUND": "Move destination not found.", "CAN'T_MOVE_FILE_TYPE": "Cannot move category into file type", "CATEGORY_MOVED_SUCCESSFULLY": "Category moved successfully.", "FAILED_TO_MOVE_CATEGORY": "Failed to move category.", "CANNOT_MOVE_FOLDER_BEYOND_10_LEVELS": "Cannot move a folders beyond 10 levels.", "CANNOT_ADD_FILE_BEYOND_10_LEVELS": "Cannot add file beyond 10 levels.", "CANNOT_MOVE_FILE_BEYOND_10_LEVELS": "Cannot move a file beyond 10 levels.", "CANNOT_MOVE_SAME_CATEGORY_INTO_SAME_CATEGORY": "Cannot move a category with same category.", "CAN'T_MOVE_FOLDER_MORE_THAN_ONE_IN_TRANING": "Cannot move more than one folder in training.", "CAN'T_MOVE_INTO_CHILD": "Cannot move into child category.", "CANNOT_MOVE_CATEGORY_INTO_ITS_DESCENDANT": "Cannot move a category into its descendant.", "CAN_ONLY_MOVE_CATEGORY_UPWARDS_IN_HIERARCHY": "Can only move categories upwards in the hierarchy.", "CATEGORY_ALREADY_EXIST": "Category already exist in destination.", "CATEGORY_DELETE_SUCCESSFULLY": "Category deleted successfully.", "CATEGORY_COPY_SUCCESSFULLY": "<PERSON>pied successfully.", "CAN'T_COPY_FOLDER_MORE_THAN_ONE_IN_TRANING": "Cannot copy more than one folder in training.", "ERROR_CANNOT_TRACK_CATEGORY_FILE_TYPE": "Cannot add track in folder type category", "TRACK_CATEGORY_SUCCESSFULLY": "Tracked successfully.", "RESTORE_TRACK_CATEGORY_SUCCESSFULLY": "Track Restored successfully", "LEAVE_TYPE_CREATION_SUCCESS": "Leave type created successfully.", "LEAVE_TYPE_UPDATION_SUCCESS": "Leave type updated successfully.", "LEAVE_TYPE_DELETION_SUCCESS": "Leave type deleted successfully.", "LEAVE_TYPE_DELETION_FAILED_IN_USE": "Leave type cannot be deleted as it is in use.", "LEAVE_TYPE_NOT_FOUND": "Leave type not found.", "LEAVE_TYPE_DUPLICATION_ERROR": "Leave type name must be unique.", "LEAVE_POLICY_CREATION_SUCCESS": "Leave policy created successfully.", "LEAVE_POLICY_UPDATION_SUCCESS": "Leave policy updated successfully.", "LEAVE_POLICY_DELETION_SUCCESS": "Leave policy deleted successfully.", "LEAVE_POLICY_DELETION_FAILED_ASSIGNED": "Leave policy cannot be deleted as it is assigned.", "LEAVE_POLICY_NOT_FOUND": "Leave policy not found.", "LEAVE_POLICY_DUPLICATION_ERROR": "Leave policy name must be unique.", "CONTRACT_TYPE_CREATION_SUCCESS": "Contract type created successfully.", "CONTRACT_TYPE_UPDATION_SUCCESS": "Contract type updated successfully.", "CONTRACT_TYPE_DELETION_SUCCESS": "Contract type deleted successfully.", "CONTRACT_TYPE_DELETION_FAILED_IN_USE": "Contract type cannot be deleted as it is in use.", "CONTRACT_TYPE_NOT_FOUND": "Contract type not found.", "CONTRACT_TYPE_DUPLICATION_ERROR": "Contract type name must be unique.", "JOB_ROLE_CREATION_SUCCESS": "Job role created successfully.", "JOB_ROLE_UPDATION_SUCCESS": "Job role updated successfully.", "JOB_ROLE_DELETION_SUCCESS": "Job role deleted successfully.", "JOB_ROLE_NOT_FOUND": "Job role not found.", "JOB_ROLE_DUPLICATION_ERROR": "Job role name must be unique.", "JOB_ROLE_DELETION_FAILED_IN_USE": "Job cannot be deleted as it is in use.", "SUCCESS_CATEGORY_ADDED_INTO_BRANCH": "Category added successfully.", "PLEASE_COMPLETE_YOUR_TRAINING_CATEGORY": "Please complete your training categories.", "CANNOT_MOVE_INTO_DIFFERENT_CATEGORY_USE": "Cannot move to a training module's subfolder.", "CANNOT_CREATE_SAME_PAYMENT_TYPE": "payment type with the same name already exists.", "PAYMENT_TYPE_CREATED": "Payment type created successfully.", "FAIL_PAYMENT_CREATED": "Failed to create payment type.", "PAYMENT_TYPE_NOT_FOUND": "Payment type not found.", "PAYMENT_TYPE_UPDATED": "Payment type updated successfully.", "FAIL_PAYMENT_UPDATED": "Failed to update payment type.", "PAYMENT_CATEGORY_EXIST_PAYMENT_TYPE": "Payment category already exist in payment type.", "PAYMENT_TYPE_ORDER_UPDATE": "Payment type order update successfully.", "PAYMENT_CATEGORY_TYPE_NOT_FOUND": "Payment type category not found.", "PAYMENT_TYPE_CATEGORY_UPDATED": "Payment type category updated successfully.", "FAIL_PAYMENT_TYPE_CATEGORY_UPDATED": "Failed to update payment type category.", "PAYMENT_CATEGORY_CREATED": "Payment type category created successfully.", "FAIL_PAYMENT_CATEGORY_CREATED": "Failed to create Payment type category.", "FIELD_EXIST_IN_CATEGORY": "Field already exist in category.", "PAYMENT_TYPE_CATEGORY_ORDER_UPDATE": "Payment type category order update successfully.", "PAYMENT_TYPE_DELETED_SUCCESSFULLY": "Payment type deleted successfully.", "FAIL_TO_DELETE_PAYMENT_TYPE": "Failed to delete payment type.", "PAYMENT_TYPE_CATEGORY_DELETED_SUCCESSFULLY": "Payment type category deleted successfully.", "FAIL_TO_DELETE_PAYMENT_TYPE_CATEGORY": "Failed to delete payment type category.", "PAYMENT_TYPE_CATEGORY_NOT_IN_BRANCH": "Payment type category not found in branch.", "FAIL_TO_ADD_VALUE_IN_CATEGORY": "Failed to add value in field.", "VALUE_ADDED_SUCCESSFULLY": "Value added successfully.", "PAYMENT_VALUE_ARRAY_CAN'T_BE_NULL": "Payment values cannot be null.", "VALUE_UPDATE_SUCCESSFULLY": "Value updated successfully.", "FAIL_TO_UPDATE_VALUE_IN_CATEGORY": "Failed to update value in field.", "CATEGORY_ID_ARRAY_REQUIRED": "Category ids required.", "DEFAULT_ACTIVE_UPDATE": "Category default active status updated.", "PAYMENT_TYPE_VALUE_NOT_FOUND": "Payment type category value not found.", "PAYMENT_TYPE_VALUE_ORDER_UPDATE": "Payment type category value order updated.", "CAN'T_REMOVE_CATEGORY_VALUE": "Category is in use.you cannot delete.", "CATEGORY_REMOVED_SUCCESSFULLY": "Category removed successfully.", "FAIL_TO_REMOVE_CATEGORY": "Failed to remove category.", "WSR_ALREADY_ADDED": "WSR already exist.", "FAIL_CANNOT_ADD_WSR_AFTER": "Cannot add WSR after 8 AM today for yesterday.", "FAIL_CANNOT_ADD_WSR_BEFORE": "WSR date must be today or yesterday.", "WSR_ADDED_SUCCESSFULLY": "WSR added successfully.", "FAIL_TO_ADD_WSR": "Failed to add Wsr.", "WSR_UPDATED_REQUEST_EXIST": "WSR update request already exist.", "WSR_REQUEST_ADDED": "WSR update request sent successfully.", "FAIL_TO_ADD_WSR_REQUEST": "Failed to send WSR update request.", "WSR_UPDATED_SUCCESSFULLY": "WSR updated successfully.", "FAIL_WSR_NOT_FOUND": "WSR not found.", "WSR_DELETED_SUCCESSFULLY": "WSR deleted successfully.", "FAILED_TO_DELETE_WSR": "Failed to delete WSR.", "FAIL_CANNOT_ADD_BEYOND_TWO_WEEKS": "Failed cannot add beyond 2 weeks.", "FAIL_CANNOT_ADD_FUTURE_WEEKS": "Failed cannot add future week.", "FAIL_WSR_REQUEST_NOT_FOUND": "wSR request not found.", "WSR_REQUEST_APPROVED": "WSR request approved.", "WSR_REQUEST_REJECTED": "WSR request rejected.", "FAIL_CANNOT_ADD_MONTH_EXPENSE": "Cannot add expense after a month.", "EXPENSE_ALREADY_ADDED": "Expense already exist.", "EXPENSE_ADDED_SUCCESSFULLY": "Expense added successfully.", "FAIL_TO_ADD_EXPENSE": "Failed to add Dsr.", "FAIL_EXPENSE_NOT_FOUND": "Expense not found.", "EXPENSE_UPDATED_SUCCESSFULLY": "Expense updated successfully.", "EXPENSE_DELETED_SUCCESSFULLY": "Expense deleted successfully.", "FAILED_TO_DELETE_EXPENSE": "Failed to delete DSR.", "EXPENSE_UPDATED_REQUEST_EXIST": "Expense update request already exist.", "EXPENSE_REQUEST_ADDED": "Expense update request sent successfully.", "FAIL_TO_ADD_EXPENSE_REQUEST": "Failed to send Expense update request.", "FAIL_EXPENSE_REQUEST_NOT_FOUND": "DSR request not found.", "EXPENSE_REQUEST_APPROVED": "Expense request approved.", "EXPENSE_REQUEST_REJECTED": "Expense request rejected.", "FAIL_PAYMENT_ALREADY_USED": "This Payment type is already used in the logs book", "DATA_SAVED_SUCCESSFULLY": "Data updated successfully.", "ERR_MISSING_PARENT_PLACE": "Missing parent place.", "ERR_INVALID_TYPE": "Invalid type.", "WSR_ALREADY_EXISTS": "WSR already exists for same week.", "DSR_ALREADY_EXISTS": "DSR already exists for same date.", "EXPENSE_ALREADY_EXISTS": "Expense already exists for same month.", "ERROR_FIRST_ADD_YESTERDAY_DSR": "Please add the DSR for yesterday first.", "CAN'T_DELETE_BRANCH_LOGS_EXIST": "Logs book entry exist for this branch. You cannot delete this branch.", "FILTER_ALREADY_EXISTS": "Filter name already exist.", "FILTER_SAVED_SUCCESSFULLY": "Filter saved successfully.", "FAILED_TO_FILTER_SAVE": "Failed to save filter.", "FILTER_DELETED_SUCCESSFULLY": "Filter deleted successfully.", "FAILED_TO_DELETE_FILTER": "Failed to delete filter.", "FILTER_NOT_FOUND": "Filter not found.", "FILTER_UPDATED_SUCCESSFULLY": "Filter updated successfully.", "ERROR_RECORD_IN_USE_TABLES": "Record is in use in one or more tables and cannot be deleted.", "DASHBOARD_NAME_ALREADY_EXISTS": "Dashboard name already exist.", "DASHBOARD_SAVED_SUCCESSFULLY": "Dashboard saved successfully.", "FAIL_TO_SAVE_DASHBOARD": "Failed to save <PERSON><PERSON>.", "DASHBOARD_UPDATED_SUCCESSFULLY": "Dashboard updated successfully.", "FAIL_TO_UPDATE_DASHBOARD": "Failed to update Dashboard.", "MARK_DASHBOARD_DEFAULT": "Dashboard marked as default successfully.", "FAIL_TO_MARK_DASHBOARD": "Failed to mark dashboard as default.", "FAIL_TO_DELETE_DEFAULT_DASHBOARD": "Cannot delete default dashboard.", "DASHBOARD_DELETED_SUCCESSFULLY": "Dashboard deleted successfully.", "FAIL_TO_DELETE_DASHBOARD": "Failed to delete Dashboard.", "FAIL_TO_GET_DASHBOARD": "Failed to get Dash<PERSON>.", "DEFAULT_DASHBOARD_ALREADY_EXISTS": "Default dashboard already exist.", "FORECAST_ALREADY_EXISTS": "Forecast already exists.", "FORECAST_CREATED_SUCCESSFULLY": "Forecast created successfully.", "FORECAST_NOT_FOUND": "Forecast not found.", "FORECAST_LOCKED": "The forecast is locked and cannot be modified.", "FORECAST_UPDATED_SUCCESSFULLY": "Forecast Updated successfully.", "FORECAST_LOCKED_SUCCESSFULLY": "Forecast Locked successfully.", "CANNOT_LOCK_FORECAST_WITH_INCOMPLETE_DATA": "Cannot lock forecast with incomplete data.", "FAILED_TO_LOCK_FORECAST": "Forecast Locked successfully.", "FAILED_TO_UPDATE_FORECAST": "failed to update forecast with incomplete data.", "FORECAST_BUGDET_DATA_UPDATED_SUCCESSFULLY": "Forecast budget data updated successfully.", "FAILED_TO_UPDATE_BUGDET_DATA": "Failed to update budget data.", "FORECAST_UNLOCKED_SUCCESSFULLY": "Forecast <PERSON><PERSON> successfully.", "FAILED_TO_UNLOCKED_FORECAST": "Forecast <PERSON><PERSON> successfully.", "CANNOT_CANCEL_APPROVED_REQUEST": "Cannot cancel approved request.", "LEAVE_REQUEST_CANCELED_SUCCESSFULLY": "Leave request canceled successfully.", "LEAVE_REQUEST_CANCELED_FAILED": "Fail to cancel leave request.", "LEAVE_RULE_UPDATED_SUCCESSFULLY": "Leave rule updated successfully.", "USER_WORK_SCHEDULE_UPDATED_SUCCESSFULLY": "User work schedule updated successfully.", "FAILED_TO_UPDATE_USER_WORK_SCHEDULE": "Failed to update user work schedule.", "USER_WORK_SCHEDULE_NOT_FOUND": "User work schedule not found.", "SUCCESS_USER_CREATED": "User created successfully.", "ERROR_PLATEFORM_TYPE_REQUIRED": "Please pass the 'platform-type' as a header parameter for processing.", "HOLIDAY_TYPE_ADDED_SUCCESSFULLY": "Holiday list created successfully.", "FAIL_TO_ADD_HOLIDAY_TYPE": "Failed to add holiday type.", "HOLIDAY_TYPE_UPDATED_SUCCESSFULLY": "Holiday list updated successfully.", "FAIL_TO_UPDATE_HOLIDAY_TYPE": "Failed to update holiday list.", "HOLIDAY_TYPE_DELETED_SUCCESSFULLY": "Holiday list deleted successfully.", "FAIL_TO_DELETE_HOLIDAY_TYPE": "Failed to delete holiday list.", "HOLIDAY_TYPE_NOT_FOUND": "Holiday list not found.", "HOLIDAY_TYPE_NAME_ALREADY_EXIST": "Holiday list name already exist.", "HOLIDAY_POLICY_NAME_ALREADY_EXIST": "Holiday policy name already exist.", "HOLIDAY_POLICY_ADDED_SUCCESSFULLY": "Holiday policy added successfully.", "FAIL_TO_ADD_HOLIDAY_POLICY": "Failed to update holiday policy.", "HOLIDAY_POLICY_NOT_FOUND": "Holiday not found.", "HOLIDAY_POLICY_UPDATED_SUCCESSFULLY": "Holiday updated successfully.", "FAIL_TO_UPDATE_HOLIDAY_POLICY": "Failed to update holiday.", "HOLIDAY_POLICY_REMOVED_SUCCESSFULLY": "<PERSON> removed successfully.", "FAIL_TO_REMOVE_HOLIDAY_POLICY": "Fail to remove holiday policy.", "VALIDATION_ERRORS_FOUND": "Validation errors found", "HOLIDAY_DATA_IMPORTED_SUCCESSFULLY": "Holiday data imported successfully.", "WORKSHEET_HOLIDAY_NOT_FOUND": "Worksheet 'Holiday' not found", "HOLIDAY_TYPE_DATE_RANGE_CONFLICT": "The selected date range conflicts with an existing holiday of the same policy.", "HOLIDAY_POLICY_ASSIGN_SUCCESSFULLY": "Holiday policy assigned successfully.", "LEAVE_POLICY_ASSIGN_SUCCESSFULLY": "Leave policy assigned successfully.", "HOLIDAY_TYPE_INACTIVE_SUCCESSFULLY": "Holiday type inactive successfully.", "HOLIDAY_TYPE_ACTIVE_SUCCESSFULLY": "Holiday type active successfully.", "FAIL_TO_INACTIVE_HOLIDAY_TYPE": "Holiday type inactive successfully.", "FAIL_TO_ACTIVE_HOLIDAY_TYPE": "Holiday type active successfully.", "ERROR_STAFF_ADD_LIMIT": "Your staff limit ({{limit}}) has been exceeded. Please upgrade your subscription to add more.", "LEAVE_POLICY_ADDED_SUCCESSFULLY": "Leave policy added successfully.", "FAIL_TO_ADD_LEAVE_POLICY": "Failed to add leave policy.", "LEAVE_POLICY_UPDATED_SUCCESSFULLY": "Leave policy updated successfully.", "FAIL_TO_UPDATE_LEAVE_POLICY": "Failed to update leave policy.", "LEAVE_POLICY_DELETED_SUCCESSFULLY": "Leave policy deleted successfully.", "FAIL_TO_DELETE_LEAVE_POLICY": "Failed to delete leave policy.", "LEAVE_POLICY_NAME_ALREADY_EXIST": "Leave policy name already exist.", "ERROR_HALF_DAY_LEAVE_NOT_ALLOWED": "Your assigned leave policy does not allow half-day leave requests.", "ERROR_LEAVE_DOCUMENTS_REQUIRED": "Your assigned leave policy need documents proof for your leave request.", "ERROR_LEAVE_GENDER_RESTRICTION": "Your assigned leave policy does not allow leave request for your gender.", "ERROR_LEAVE_MARITAL_STATUS_RESTRICTION": "Your assigned leave policy does not allow leave request for your marital status.", "ERROR_HOURS_LEAVE_NOT_ALLOWED": "Your assigned leave policy does not allow more than {{hours}} hours of leave requests.", "ERROR_PAST_DATE_RESTRICTION": "Your assigned leave policy does not allow past date for your leave request.", "ERROR_LEAVE_GREATER_THAN_JOINING_RESTRICTION": "The Leave Application date should be greater than or equal to the {{joining_date}}", "ERROR_FUTURE_DATE_RESTRICTION": "Your assigned leave policy does not allow future date for your leave request.", "ERROR_LEAVE_LESS_THAN_CONTRACT_DATE_RESTRICTION": "The Leave Application date should be less than or equal to the {{contract_date}}", "ERROR_ADVANCE_LEAVE_RESTRICTION": "Your assigned leave policy requires at least {{advance_hours}} days of advance notice for leave requests.", "ERROR_MAX_CONSECUTIVE_LEAVE_RESTRICTION": "Your assigned leave policy does not allow leave requests for more than {{max_consecutive_days}} consecutive days.", "ERROR_GAP_BETWEEN_LEAVE_RESTRICTION": "As per your policy, you cannot apply for consecutive days of leave and must have a {{leave_gap}}-day gap between two leave applications.", "ERROR_MAX_LEAVE_RESTRICTION": "As per your leave policy, you have reached the maximum allowed leaves {{max_leaves_allowed}} for the {{policy_type}}.", "ERROR_NOTICE_PERIOD_USER_LEAVE_RESTRICTION": "As per the leave policy, leave requests cannot be submitted during the notice period.", "ERROR_POLICY_NOT_ASSIGNED": "you have not assigned any policy", "POLICY_SETTING_UPDATED_SUCCESSFULLY": "Policy setting updated successfully", "POLICY_SETTING_CREATED_SUCCESSFULLY": "Policy setting created successfully", "POLICY_SETTING_NOT_FOUND": "Policy setting not found", "CONTRACT_NAME_DUPLICATION_ERROR": "Contract name must be unique.", "CONTRACT_CREATED_SUCCESSFULLY": "Contract name created successfully.", "FAIL_TO_CREATE_CONTRACT": "Failed to create contract name.", "ERROR_YOU_NOT_APPLY_LEAVE_ON_HOLIDAY": "Leave application isn’t applicable on holidays.", "CONTRACT_DELETED_SUCCESSFULLY": "Contract name removed successfully.", "ERROR_LEAVE_CANNOT_APPLY_WEEKEND": "Leave cannot be taken on days off and holidays as per your policy.", "LEAVE_POLICY_OVER": "Leave policy over", "LEAVE_POLICY_NOT_STARTED": "Leave policy not started", "LEAVE_ENTITLMENT_NOT_ASSIGN": "You can apply for leave after {{pendingDays}} days because of the new employment policy.", "PROBATION_LEAVE_RESTRIC": "You probation leave balance exceeds.", "LEAVE_POLICY_BALANCE_STOPPED": "Leave accrual stopped: Maximum leave balance of {{leave_limit}} days reached.", "CANNOT_LEAVE_BEYOND_SERVING_DATE ": "You cannot apply for leave beyond your last serving date.", "YOUR_POLICY_BALANCE_EXCEEDS": "Your leave policy balance exceeds.", "ERROR_YOU_DO_NOT_HAVE_ENOUGH_LEAVE_BALANCE": "You don’t have enough leave available for this request.", "ERROR_FAIL_LEAVE_ALREADY_APPLIED": "Your leave request for the same day has already been already approved.", "ERROR_PAST_HOURS_LEAVE_NOT_ALLOWED": "You cannot request more than {{hours}} hours for past leave. Please adjust your request.", "ERROR_CONTRACT_EXPIRE": "Your contract has been expired.you cannot apply for leave.", "ERROR_FORECAST_HISTORY_NOT_FOUND": "Forecast history not found", "SUCCESS_FORECAST_ASSIGNED": "Forecast assigned to user successfully ", "ERROR_FORECAST_ALREADY_APPROVED": "The forecast has been approved and is now locked,it cannot be {{type}} anymore", "ERROR_FORECAST_UNDER_APPROVAL": "The forecast is under approval,it cannot be {{type}} anymore", "SUCCESS_BUDGET_DELETED": "Forecast has been deleted successfully", "SUCCESS_USER_ONBOARDING_RESET": "User onboarding reset successfully.", "DOCUMENT_REMOVED_SUCCESSFULLY": "User onboarding documents removed successfully.", "ERROR_BUDGET_ALREADY_ASSIGNED": "This budget already assigned to this user.", "EMP_CONTRACT_HISTROY_DELETED_SUCCESSFULLY": "Employment contract history deleted successfully.", "EXPENSE_DATA_PROCESSED_SUCCESSFULLY": "Expense data processed successfully.", "ERROR_NO_FORECAST_ASSIGNED": "Currently, there is no assigned budget.", "SUCCESS_BUDGET_REVOKED": "Successfully budget revoked from user", "CANNOT_DELETE_DEPARTMENT_USE_IN_CONTRACT": "Department deletion denied: still in use by {{userLength}} user{{plural}} via contract.", "CANNOT_MOVE_CONTRACT_DEPARTMENT": "Contract moving denied: still in use by {{userLength}} user{{plural}}.", "NO_USERS_FOUND": "Users not found.", "SUCCESS_BANNER_CREATION": "Banner notification created successfully.", "SUCCESS_BANNER_UPDATION": "Banner notification updated successfully.", "SUCCESS_BANNER_DELETION": "Banner notification deleted successfully.", "ERROR_BANNER_NOT_FOUND": "Banner notification not found.", "SUCCESS_DATA_FETCHED": "Data fetched successfully.", "SUCCESS_BRANCH_DELETATION": "branch deleted Successfully.", "SUCCESS_BANNER_CONFIG_CREATION": "Banner config created successfully.", "SUCCESS_BANNER_CONFIG_UPDATION": "Banner config updated successfully.", "SUCCESS_BANNER_CONFIG_DELETION": "Banner config deleted successfully.", "ERROR_BANNER_CONFIG_NOT_FOUND": "Banner config not found.", "SUCCESS_BANNER_CONFIG_FETCHED": "Banner config fetched successfully.", "SUCCESS_BANNER_READ": "Banner notification marked as read successfully.", "FAIL_INCREASE_MANUALLY": "Action blocked due to insufficient leave balance. Please verify the current balance first.", "AT_LEAST_ONE_FILE_REQUIRED": "At least one file is required.", "SIDE_LATTER_ADDED_SUCCESSFULLY": "Side latter added successfully.", "SIDE_LETTER_ID_REQUIRED": "Side letter id is required.", "SIDE_LETTER_NOT_FOUND_OR_ALREADY_PROCESSED": "Side letter not found or already processed.", "SIDE_LETTER_PROCESSED_SUCCESSFULLY": "Side letter processed successfully.", "SIDE_LETTER_DELETED_SUCCESSFULLY": "Side letter deleted successfully.", "CONFIRMATION_STATUS_REQUIRED": "Side letter Confirmation required.", "RECIPIENT_REQUIRED": " Recipient required.", "SIDE_LETTER_TITLE_REQUIRED": "Side letter title required.", "SIDE_LETTER_TITLE_ALREADY_EXIST": "Side letter title already exist.", "SIDE_LETTER_APPROVED": "Side letter confirmed successfully.", "ERROR_STORAGE_LIMIT_EXCEEDED": "Your file size limit exceeds your current storage size. Please delete some files or upgrade your plan to increase storage capacity.", "EMPLOYMENT_NUMBER_REQUIRED": "Employment number required.", "EMPLOYMENT_NUMBER_ALREADY_EXIST": "Employment number already exists. Please use a unique number.", "HEALTH_SAFETY_BRANCH_STATUS_UPDATED": "Health and safety branch assign status updated.", "SUCCESS_USER_PIN_UPDATED": "User pin updated.", "SOME_USER_HAVE_ALREADY_ASSIGNED_THIS_POLICY": "Cannot delete this leave policy as it's assigned to users. Unassign it first.", "HOLIDAY_POLICY_ASSIGN_UPDATED_SUCCESSFULLY": "Holiday policy assignment updated successfully.", "SUCCESS_CONTRACT_DELETED": "Contract deleted successfully.", "SUCCESS_CONTRACT_CREATED": "Contract created successfully.", "SUCCESS_CONTRACT_UPDATED": "Contract updated successfully.", "SUCCESS_CONTRACT_MOVED": "Contract moved successfully.", "SUCCESS_CONTRACT_COPIED": "Contract copied successfully.", "SUCCESS_HOLIDAY_CREATED": "Holiday created successfully.", "SOME_USER_HAVE_ALREADY_ASSIGNED_HOLIDAY_POLICY": "Cannot delete this holiday policy as it's assigned to users. Unassign it first.", "CANNOT_DELETE_CATEGORY_WITH_BRANCH_ASSIGNMENTS": "Cannot delete category because it is assigned to one or more branches.", "CANNOT_DELETE_CATEGORY_WITH_DEPARTMENT_ASSIGNMENTS": "Cannot delete category because it is assigned to one or more department.", "USER_REINVITED_SUCCESSFULLY": "User reinvited successfully.", "CAN'T_MOVE_FILE_MORE_THAN_ONE_IN_TRANING": "Cannot move File in First level of training documents.", "CAN'T_COPY_FILE_MORE_THAN_ONE_IN_TRANING": "Cannot Copy File in First level of training documents.", "SUCCESS_USER_WEEK_DAY_ENTRY": "User week day entry data created successfully.", "PERMISSIONS_FETCHED_SUCCESSFULLY": "Permission fetched successfully.", "ROLE_CREATED_SUCCESSFULLY": "Role created successfully.", "ROLE_ALREADY_EXISTS": "Role already exists.", "ROLE_UPDATED_SUCCESSFULLY": "Role updated successfully.", "ROLE_SET_INACTIVE_SUCCESSFULLY": "Role set to inactive successfully.", "MODULE_ALREADY_EXISTS_FOR_ROLE": "<PERSON><PERSON><PERSON> already exists for this role.", "PERMISSION_CREATED_SUCCESSFULLY": "Permission created successfully.", "PERMISSION_NOT_FOUND": "Permission not found.", "PERMISSION_UPDATED_SUCCESSFULLY": "Permission updated successfully.", "PERMISSION_DELETED_SUCCESSFULLY": "Permission deleted successfully.", "NO_PERMISSIONS_FOUND_FOR_FROM_ROLE": "No permissions found for the source role.", "DUPLICATE_MODULES_FOUND_FOR_TO_ROLE": "Duplicate modules found for the target role.", "PERMISSIONS_COPIED_SUCCESSFULLY": "Permissions copied successfully.", "MODULE_ALREADY_EXISTS": "Module already exists.", "MODULE_CREATED_SUCCESSFULLY": "<PERSON><PERSON><PERSON> created successfully.", "MODULE_NOT_FOUND": "<PERSON><PERSON><PERSON> not found.", "MODULE_UPDATED_SUCCESSFULLY": "Module updated successfully.", "MODULE_DELETED_SUCCESSFULLY": "<PERSON><PERSON><PERSON> deleted successfully.", "USER_PROFILE_PARTIALLY_UPDATED_CHANGE_REQUEST_CREATED": "Profile update and change request created successfully.", "ONBOARDING_FORM_UPDATED_CHANGE_REQUEST_CREATED": "Onboarding form updated successfully and change request created successfully.", "CHANGE_REQUEST_ALREADY_PENDING": "Change request already pending for fields: {{fields}}", "SUCCESS_DATA_UPDATED": "Data updated successfully.", "USER_PROFILE_UPDATED_SUCCESSFULLY": "User profile updated successfully."}