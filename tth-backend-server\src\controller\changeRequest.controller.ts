import { StatusCodes } from "http-status-codes";
import { Op, QueryTypes, Sequelize } from "sequelize";
import { getPaginatedItems, getPagination } from "../helper/utils";
import { sequelize } from "../models";
import { Branch } from "../models/Branch";
import { ChangeRequest, change_request_status as changeRequestStatus } from "../models/ChangeRequest";
import { ChangeRequestHistory, change_request_history_status } from "../models/ChangeRequestHistory";
import { User, user_status } from "../models/User";
import changeRequestValidator from "../validators/changeRequest.validator";
import { createNotification, getUserFullName } from "../helper/common";
import { NOTIFICATIONCONSTANT, NOTIFICATION_TYPE, ROLE_CONSTANT, REDIRECTION_TYPE, FILE_UPLOAD_CONSTANT, RABBITMQ_QUEUE } from "../helper/constant";
import { Department } from "../models/Department";
import { moveFileInBucket } from "../helper/upload.service";
import { Item } from "../models/Item";
import { RightToWorkCheckList, status } from "../models/RightToWorkCheckList";
import { StarterForm } from "../models/StarterForm";
import { HrmcForm } from "../models/HrmcForm";
import { ChangeRequestSettings } from "../models/ChangeRequestSettings";
import rabbitmqPublisher from "../rabbitmq/rabbitmq";

/**
 *  Send change request
 * @param req
 * @param res
 * @returns
 */

const sendChangeRequest = async (req: any, res: any) => {
  try {
    const { change_request_subject = "", old_data, new_data } = req.body;
    const { change_request_id } = req.params;
    const fileIds: string[] = [];
    if (req.files && req.files.length > 0) {
      // Process and move files to the appropriate S3 location
      for (const file of req.files) {
        if (file.isMovable) {
          await moveFileInBucket(
            file.bucket,
            file.path,
            FILE_UPLOAD_CONSTANT.CHANGE_REQUEST_FILES.destinationPath(
              req.user.organization_id,
              req.user.id.toString(),
              file.filename,
            ),
            file.item_id,
            true,
          );
        }
        fileIds.push(file.item_id);
      }
    }

    const { error } = await changeRequestValidator.sendChangeRequest.validate(
      req.body,
    );
    if (error) {
      return res
        .status(400)
        .json({ status: false, message: error.details[0].message });
    }
    const findHrUsers =
      (await User.findAll({
        attributes: ['id', 'webAppToken', 'appToken'],
        where: {
          user_status: {
            [Op.not]: [
              user_status.DELETED,
              user_status.PENDING,
              user_status.CANCELLED,
            ],
          },
          id: {
            [Op.in]: [
              sequelize.literal(
                `(SELECT nv_user_roles.user_id from nv_user_roles where nv_user_roles.role_id IN (SELECT id from nv_roles where role_name IN ('${ROLE_CONSTANT.HR}')))`,
              ),
            ],
          },
          organization_id: req.user.organization_id
        }, raw: true,
        group: ['id']
      })) || [];
    if (change_request_id) {
      const findRequestExist = await ChangeRequest.findOne({ attributes: ['id'], where: { id: change_request_id, change_request_status: changeRequestStatus.REJECTED }, raw: true })
      if (findRequestExist) {
        const fileIdsStr = fileIds.length > 0 ? JSON.stringify(fileIds) : "";
        const updateChangeRequest = await ChangeRequest.update({ change_request_subject: change_request_subject, old_data, new_data, change_request_status: changeRequestStatus.REOPENED, user_id: req.user.id, updated_by: req.user.id, change_request_files: fileIdsStr }, { where: { id: findRequestExist.id } })
        if (updateChangeRequest.length > 0) {
          await ChangeRequestHistory.create({ change_request_id: findRequestExist.id, change_request_subject: change_request_subject, old_data, new_data, change_request_status: changeRequestStatus.REOPENED, change_request_history_status: change_request_history_status.ACTIVE, user_id: req.user.id, created_by: req.user.id, updated_by: req.user.id, change_request_files: fileIdsStr } as any)
          const getFullName = await getUserFullName(req.user.id)
          await createNotification(findHrUsers, req, NOTIFICATION_TYPE.INDIVIDUAL, NOTIFICATIONCONSTANT.CHANGE_REQUEST.content(getFullName), NOTIFICATIONCONSTANT.CHANGE_REQUEST.heading, REDIRECTION_TYPE.CHANGE_REQUEST, findRequestExist.id, { change_request_id: findRequestExist.id })
          return res
            .status(StatusCodes.OK)
            .json({ status: true, message: res.__("CHANGE_REQUEST_SEND_SUCCESSFULLY") });

        } else {
          return res
            .status(StatusCodes.EXPECTATION_FAILED)
            .json({ status: false, message: res.__("CHANGE_REQUEST_SEND_FAILED") });
        }
      }
    }
    const fileIdsStr = fileIds.length > 0 ? JSON.stringify(fileIds) : "";
    const sendChangeRequest = await ChangeRequest.create({ change_request_subject: change_request_subject, old_data, new_data, change_request_status: changeRequestStatus.PENDING, user_id: req.user.id, created_by: req.user.id, updated_by: req.user.id, change_request_files: fileIdsStr } as any)
    if (sendChangeRequest) {
      await ChangeRequestHistory.create({ change_request_id: sendChangeRequest.id, change_request_subject: change_request_subject, old_data, new_data, change_request_status: sendChangeRequest.change_request_status, change_request_history_status: change_request_history_status.ACTIVE, user_id: req.user.id, created_by: req.user.id, updated_by: req.user.id, change_request_files: fileIdsStr } as any)
      const getFullName = await getUserFullName(req.user.id)
      await createNotification(findHrUsers, req, NOTIFICATION_TYPE.INDIVIDUAL, NOTIFICATIONCONSTANT.CHANGE_REQUEST.content(getFullName), NOTIFICATIONCONSTANT.CHANGE_REQUEST.heading, REDIRECTION_TYPE.CHANGE_REQUEST, sendChangeRequest.id, { change_request_id: sendChangeRequest.id })
      return res
        .status(StatusCodes.OK)
        .json({ status: true, message: res.__("CHANGE_REQUEST_SEND_SUCCESSFULLY") });

    } else {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("CHANGE_REQUEST_SEND_FAILED") });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};


const getAllChangeRequest = async (req: any, res: any) => {
  try {
    const { page, size, change_request_status, change_request_date, search }: any = req.query
    const { limit, offset } = getPagination(page, size);

    const whereObj: any = { change_request_status: { [Op.not]: changeRequestStatus.DELETED } }
    const getUserDetail: any = await User.findOne({
      attributes: ['id', 'organization_id', 'web_user_active_role_id'],
      where: { id: req.user.id, organization_id: req.user.organization_id }, raw: true
    });
    if (!getUserDetail) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("ERROR_USER_NOT_FOUND"),
        data: {},
      });
    }
    // Check if nv_roles table exists
    const checkTableExistQuery = `SHOW TABLES LIKE 'nv_roles';`;
    const tableExists = await sequelize.query(checkTableExistQuery, {
      type: QueryTypes.SHOWTABLES
    });

    if (!tableExists || tableExists.length === 0) {
      throw new Error("Table 'nv_roles' doesn't exist in database.");
    }
    //Construct recursive query to find child roles
    const getChildRolesQuery = `
        WITH RECURSIVE ChildRoles AS (
        SELECT id, role_name, parent_role_id
        FROM nv_roles WHERE id = :activeRoleId
        UNION ALL
        SELECT r.id, r.role_name, r.parent_role_id
        FROM nv_roles r
        INNER JOIN ChildRoles cr ON r.parent_role_id = cr.id
        )
        SELECT id
        FROM ChildRoles
        WHERE id != :activeRoleId`;

    // Execute recursive query to find child roles
    const getChildRoles = await sequelize.query(getChildRolesQuery, {
      replacements: { activeRoleId: getUserDetail.web_user_active_role_id },
      type: QueryTypes.SELECT,
    });

    // Build WHERE clause for user roles based on child roles
    let whereStr = '';
    getChildRoles.forEach((child_role: any, index: number) => {
      if (index > 0) {
        whereStr += ' OR ';
      }
      whereStr += `FIND_IN_SET(${child_role.id}, (SELECT GROUP_CONCAT(role_id) FROM nv_user_roles WHERE user_id = change_request_user.id)) > 0`;
    });
    if (whereStr) {
      whereObj[Op.and] = [sequelize.literal(`(${whereStr})`)];
    }
    const changeRequestObj: any = {
      attributes: ['change_request_status', 'id', 'change_request_subject', 'createdAt'],
      order: [['updatedAt', 'DESC']],
      include: [
        {
          model: User,
          as: "change_request_user",
          attributes: [
            'id', [
              sequelize.fn(
                "concat",
                sequelize.col("user_first_name"),
                " ",
                sequelize.col("user_last_name"),
              ),
              "user_full_name",
            ],
            "user_email",
            [
              sequelize.literal(
                `CASE 
            WHEN user_avatar IS NULL OR user_avatar = '' THEN ''
            WHEN NOT user_avatar REGEXP '^[0-9]+$' OR NOT EXISTS (SELECT 1 FROM nv_items WHERE id = change_request_user.user_avatar) 
            THEN CONCAT('${global.config.API_BASE_URL}user_avatars/', change_request_user.user_avatar)
            ELSE CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE id = change_request_user.user_avatar))
          END`
              ),
              "user_avatar_link",
            ],
            "user_avatar",
            [sequelize.literal(`(change_request_user.user_avatar)`), "user_avatar_id"],
            "employment_number",
          ],
          where: {
            organization_id: getUserDetail.organization_id, // Ensure the user belongs to the same organization
          },
          include: [
            {
              model: Branch,
              as: "branch",
              attributes: ["id", "branch_name"],
              where: {
                organization_id: getUserDetail.organization_id
              }
            },
            {
              model: Department,
              as: "department",
              attributes: ["id", "department_name"],
              where: {
                organization_id: getUserDetail.organization_id
              }
            }
          ]
        }
      ], where: whereObj
    }
    if (page && size) {
      changeRequestObj.limit = Number(limit);
      changeRequestObj.offset = Number(offset);
    }
    if (change_request_status) {
      changeRequestObj.where.change_request_status = change_request_status
    }
    if (change_request_date) {
      changeRequestObj.where.createdAt = sequelize.where(sequelize.fn('DATE', sequelize.col('ChangeRequest.createdAt')), {
        [Op.eq]: change_request_date
      });
    }
    // Search
    if (search) {
      whereObj[Op.or] = [
        Sequelize.where(
          Sequelize.fn(
            "concat",
            Sequelize.col("user_first_name"),
            " ",
            Sequelize.col("user_last_name"),
          ),
          {
            [Op.like]: `%${search}%`,
          },
        ),
        Sequelize.where(
          Sequelize.col("user_email"),
          {
            [Op.like]: `%${search}%`,
          },
        )
      ];
    }
    const { count, rows: changeRequestList } = await ChangeRequest.findAndCountAll(changeRequestObj);
    const { total_pages } = getPaginatedItems(
      size,
      page,
      count || 0,
    );
    return res.status(StatusCodes.OK).json({
      status: true,
      data: changeRequestList,
      message: res.__("SUCCESS_FETCHED"),
      count: count,
      page: parseInt(page),
      size: parseInt(size),
      total_pages,
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const updateRejectedRequest = async (req: any, res: any) => {
  try {
    const { change_request_subject = "", old_data, new_data } = req.body;
    const { change_request_id } = req.params
    const { error } = await changeRequestValidator.sendChangeRequest.validate(req.body);
    if (error) {
      return res
        .status(400)
        .json({ status: false, message: error.details[0].message });
    }
    const findRequestExist = await ChangeRequest.findOne({
      attributes: ['id', 'change_request_files', 'user_id'],
      where: {
        id: change_request_id,
        change_request_status: { [Op.not]: changeRequestStatus.DELETED },
      }, raw: true
    });
    const fileIds: string[] = [];
    if (req.files && req.files.length > 0) {
      // Process and move files to the appropriate S3 location
      for (const file of req.files) {
        if (file.isMovable) {
          await moveFileInBucket(
            file.bucket,
            file.path,
            FILE_UPLOAD_CONSTANT.CHANGE_REQUEST_FILES.destinationPath(
              req.user.organization_id,
              req.user.id.toString(),
              file.filename,
            ),
            file.item_id,
            true,
          );
        }
        fileIds.push(file.item_id);
      }
    }

    if (findRequestExist) {
      const fileIdsStr =
        fileIds.length > 0
          ? JSON.stringify(fileIds)
          : findRequestExist.change_request_files;
      const updateChangeRequest = await ChangeRequest.update(
        {
          change_request_subject: change_request_subject,
          old_data,
          new_data,
          change_request_status: findRequestExist.change_request_status,
          user_id: findRequestExist.user_id,
          updated_by: req.user.id,
          change_request_files: fileIdsStr,
        },
        { where: { id: findRequestExist.id } },
      );
      if (updateChangeRequest.length > 0) {
        await ChangeRequestHistory.create({
          change_request_id: findRequestExist.id,
          change_request_subject: change_request_subject,
          old_data,
          new_data,
          change_request_status: findRequestExist.change_request_status,
          change_request_history_status: change_request_history_status.ACTIVE,
          user_id: req.user.id,
          created_by: req.user.id,
          updated_by: req.user.id,
          change_request_files: fileIdsStr,
        } as any);
        return res
          .status(StatusCodes.OK)
          .json({ status: true, message: res.__("CHANGE_REQUEST_SEND_SUCCESSFULLY") });

      } else {
        return res
          .status(StatusCodes.EXPECTATION_FAILED)
          .json({ status: false, message: res.__("CHANGE_REQUEST_SEND_FAILED") });
      }
    } else {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("CHANGE_REQUEST_SEND_FAILED") });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const getChangeRequestById = async (req: any, res: any) => {
  try {
    const { change_request_id } = req.params
    let findRequestExist: any = await ChangeRequest.findOne({
      include: [
        {
          model: User,
          as: "change_request_user",
          attributes: [
            'id', [
              sequelize.fn(
                "concat",
                sequelize.col("user_first_name"),
                " ",
                sequelize.col("user_last_name"),
              ),
              "user_full_name",
            ],
            "user_email",
            "employment_number",
            [
              sequelize.literal(
                `CASE 
                                  WHEN user_avatar IS NULL OR user_avatar = '' THEN ''
                                  WHEN NOT user_avatar REGEXP '^[0-9]+$' OR NOT EXISTS (SELECT 1 FROM nv_items WHERE id = change_request_user.user_avatar) 
                                  THEN CONCAT('${global.config.API_BASE_URL}user_avatars/', user_avatar)
                                  ELSE CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE id = change_request_user.user_avatar))
                                END`
              ),
              "user_avatar_link",
            ],
            "user_avatar",
          ],
          where: {
            organization_id: req.user.organization_id
          },
          include: [
            {
              model: Branch,
              as: "branch",
              attributes: ["id", "branch_name"],
              where: {
                organization_id: req.user.organization_id
              }
            }
          ],
        }
      ],
      attributes: ['id', 'old_data', 'change_request_subject', 'change_request_files', 'new_data', 'change_request_status', 'createdAt'], where: { id: change_request_id }
    })
    findRequestExist = JSON.parse(JSON.stringify(findRequestExist))
    if (findRequestExist) {
      const findRequestHistory = await ChangeRequestHistory.findAll({
        attributes: [
          "id",
          "old_data",
          "new_data",
          "change_request_status",
          "change_request_files",
          "change_request_subject",
          "change_request_remark",
          "createdAt",
        ],
        where: {
          change_request_id: findRequestExist.id,
          change_request_history_status: change_request_history_status.ACTIVE,
        },
        order: [["updatedAt", "DESC"]],
      });

      const responseData = {
        ...findRequestExist,
        request_history: [],
        change_request_files: [],
        baseUrl: `${global.config.API_BASE_URL}`,
      };

      if (findRequestHistory.length > 0) {
        responseData.request_history = await Promise.all(
          findRequestHistory.map(async (requestHistory) => {
            const historyData = JSON.parse(JSON.stringify(requestHistory));

            // Process files for history records
            try {
              const historyFileIds = JSON.parse(
                historyData.change_request_files,
              );
              historyFileIds.map((ids: any) => Number(ids));
              if (historyFileIds && historyFileIds.length > 0) {
                const items = await Item.findAll({
                  where: { id: { [Op.in]: historyFileIds } },
                  attributes: ["id", "item_location"],
                });

                // Create a map of existing items
                const itemMap = new Map(items.map((item: any) => [item.id, item.item_location]));
                // Map each file ID to either its item location or fallback path
                historyData.change_request_files = historyFileIds.map((fileId: string | number) => {
                  return itemMap.has(Number(fileId))
                    ? `${itemMap.get(Number(fileId))}`
                    : `change_request/${fileId}`;
                });
              } else {
                historyData.change_request_files = [];
              }
            } catch (err) {
              historyData.change_request_files = [];
            }

            return historyData;
          }),
        );
      }

      // Process files for main request
      if (
        findRequestExist.change_request_files &&
        findRequestExist.change_request_files !== ""
      ) {
        try {
          const mainFileIds = JSON.parse(findRequestExist.change_request_files);
          mainFileIds.map((ids: any) => Number(ids));
          if (mainFileIds && mainFileIds.length > 0) {
            const items = await Item.findAll({
              where: { id: { [Op.in]: mainFileIds } },
              attributes: ["id", "item_location"],
            });

            // Create a map of existing items
            const itemMap = new Map(items.map((item: any) => [item.id, item.item_location]));

            // Map each file ID to either its item location or fallback path
            responseData.change_request_files = mainFileIds.map((fileId: string | number) => {
              return itemMap.has(Number(fileId))
                ? `${itemMap.get(Number(fileId))}`
                : `change_request/${fileId}`;
            });
          }
        } catch (err) {
          responseData.change_request_files = [];
        }
      }

      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("SUCCESS_FETCHED"),
        data: responseData,
      });
    } else {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("FAIL_DATA_NOT_FOUND"),
        data: {},
      });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};


const getOwnChangeRequestList = async (req: any, res: any) => {
  try {
    const { page, size, change_request_status, change_request_date, search }: any = req.query
    const { limit, offset } = getPagination(page, size);
    const whereObj: any = { user_id: req.user.id, change_request_status: { [Op.not]: changeRequestStatus.DELETED } }
    const changeRequestObj: any = {
      attributes: ['change_request_status', 'id', 'change_request_subject', 'createdAt'],
      order: [['updatedAt', 'DESC']]
      , include: [
        {
          model: User,
          as: "change_request_user",
          attributes: [
            'id', [
              sequelize.fn(
                "concat",
                sequelize.col("user_first_name"),
                " ",
                sequelize.col("user_last_name"),
              ),
              "user_full_name",
            ],
            "user_email",
            "employment_number",
            [
              sequelize.literal(
                `CASE 
            WHEN user_avatar IS NULL OR user_avatar = '' THEN ''
            WHEN NOT user_avatar REGEXP '^[0-9]+$' OR NOT EXISTS (SELECT 1 FROM nv_items WHERE id = change_request_user.user_avatar) 
            THEN CONCAT('${global.config.API_BASE_URL}user_avatars/', change_request_user.user_avatar)
            ELSE CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE id = change_request_user.user_avatar))
          END`
              ),
              "user_avatar_link",
            ],
            [sequelize.literal(`(change_request_user.user_avatar)`), "user_avatar_id"],
          ],
          where: {
            organization_id: req.user.organization_id
          },
          include: [
            {
              model: Branch,
              as: "branch",
              attributes: ["id", "branch_name"],
              where: {
                organization_id: req.user.organization_id
              }
            }
          ]
        }
      ], where: whereObj
    }


    if (page && size) {
      changeRequestObj.limit = Number(limit);
      changeRequestObj.offset = Number(offset);
    }
    if (change_request_status) {
      changeRequestObj.where.change_request_status = change_request_status
    }
    if (change_request_date) {
      changeRequestObj.where.createdAt = sequelize.where(sequelize.fn('DATE', sequelize.col('ChangeRequest.createdAt')), {
        [Op.eq]: change_request_date
      });
    }
    // Search
    if (search) {
      whereObj[Op.or] = [
        Sequelize.where(
          Sequelize.fn(
            "concat",
            Sequelize.col("user_first_name"),
            " ",
            Sequelize.col("user_last_name"),
          ),
          {
            [Op.like]: `%${search}%`,
          },
        ),
        Sequelize.where(
          Sequelize.col("user_email"),
          {
            [Op.like]: `%${search}%`,
          },
        )
      ];
    }
    const { count, rows: changeRequestList } = await ChangeRequest.findAndCountAll(changeRequestObj);
    const { total_pages } = getPaginatedItems(
      size,
      page,
      count || 0,
    );
    return res.status(StatusCodes.OK).json({
      status: true,
      data: changeRequestList,
      message: res.__("SUCCESS_FETCHED"),
      count: count,
      page: parseInt(page),
      size: parseInt(size),
      total_pages,
    });

  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
}

const approveRejectChangeRequest = async (req: any, res: any) => {
  try {
    const { change_request_status, change_request_remark } = req.body
    const { error } = await changeRequestValidator.approveRejectRequest.validate(req.body);
    if (error) {
      return res
        .status(400)
        .json({ status: false, message: error.details[0].message });
    }
    const { change_request_id } = req.params
    const findRequestExist: any = await ChangeRequest.findOne({ attributes: ['id', 'change_request_files', 'user_id', 'change_request_subject', 'old_data', 'new_data'], where: { id: change_request_id }, raw: true })
    if (findRequestExist) {
      /** check if status is approved, then update status and requested fields data directly in database */
      if (change_request_status == changeRequestStatus.APPROVED) {
        /** parse new data */
        const newData = JSON.parse(findRequestExist.new_data)
        /** update user data directly in database */
        if (newData.username || newData.user_first_name || newData.user_last_name || newData.user_email || newData.user_middle_name || newData.address_line1 || newData.address_line2 || newData.country ||
          newData.user_gender || newData.user_gender_other || newData.marital_status || newData.marital_status_other || newData.date_of_birth || newData.emergency_contact || newData.user_phone_number ||
          newData.pin_code || newData.geo_country || newData.geo_state || newData.geo_city || newData.user_signature || newData.user_avatar) {
          const updateUser = await User.update(newData, { where: { id: findRequestExist.user_id } })
          if (updateUser.length > 0) {
            /** update change request status */
            await ChangeRequest.update({ change_request_status: change_request_status }, { where: { id: findRequestExist.id } })
            /** create change request history */
            await ChangeRequestHistory.create({ change_request_id: findRequestExist.id, change_request_subject: findRequestExist.change_request_subject, old_data: findRequestExist.old_data, new_data: findRequestExist.new_data, change_request_status: change_request_status, change_request_history_status: change_request_history_status.ACTIVE, user_id: req.user.id, created_by: req.user.id, updated_by: req.user.id, change_request_files: findRequestExist.change_request_files, change_request_remark: change_request_remark } as any)

            /** Check if newData contains any of the 5 fields that need to be sent to queue */
            const queueFields = {
              username: newData.username || null,
              user_first_name: newData.user_first_name || null,
              user_last_name: newData.user_last_name || null,
              user_email: newData.user_email || null,
              user_phone_number: newData.user_phone_number || null,
            };

            // Check if any of the 5 fields exist in newData
            const hasQueueFields = Object.values(queueFields).some(value => value !== null && value !== undefined && value !== "");

            if (hasQueueFields) {
              // Get user's keycloak_auth_id for the queue message
              const userDetail = await User.findOne({
                attributes: ['keycloak_auth_id'],
                where: { id: findRequestExist.user_id },
                raw: true
              });

              if (userDetail?.keycloak_auth_id) {
                /** Prepare message for queue */
                const message = {
                  username: queueFields.username,
                  user_first_name: queueFields.user_first_name,
                  user_last_name: queueFields.user_last_name,
                  user_email: queueFields.user_email,
                  user_phone_number: queueFields.user_phone_number,
                  keycloak_auth_id: userDetail.keycloak_auth_id,
                  type: "staff_update",
                };
                /** Publish a message to the "staff creation/update" queue */
                const queue: any = RABBITMQ_QUEUE.STAFF_CREATION_DETAILS;
                await rabbitmqPublisher.publishMessage(queue, message);
              }
            }
          }
        } else if (newData.has_right_to_work_in_uk || newData.is_uk_citizen || newData.is_confirm_upload || newData.photoID || newData.ni_letter || newData.statements_dl_utility || newData.cv || newData.p45 || newData.share_code || newData.passport_front || newData.passport_back || newData.brp_front || newData.brp_back || newData.student_letter) {
          const updateRightToWorkFormDetail = await RightToWorkCheckList.update(newData, { where: { user_id: findRequestExist.user_id, checklist_id: 1, status: status.ACTIVE } })
          if (updateRightToWorkFormDetail.length > 0) {
            /** update change request status */
            await ChangeRequest.update({ change_request_status: change_request_status }, { where: { id: findRequestExist.id } })
            /** create change request history */
            await ChangeRequestHistory.create({ change_request_id: findRequestExist.id, change_request_subject: findRequestExist.change_request_subject, old_data: findRequestExist.old_data, new_data: findRequestExist.new_data, change_request_status: change_request_status, change_request_history_status: change_request_history_status.ACTIVE, user_id: req.user.id, created_by: req.user.id, updated_by: req.user.id, change_request_files: findRequestExist.change_request_files, change_request_remark: change_request_remark } as any)
          }
        } else if (newData.insurance_number || newData.postgraduate_loan || newData.statement_apply || newData.is_current_information || newData.another_job || newData.private_pension || newData.payment_from || newData.load_guidance || newData.statementA || newData.statementB || newData.statementC) {
          const updateHrmcFormDetail = await HrmcForm.update(newData, { where: { user_id: findRequestExist.user_id, status: status.ACTIVE, checklist_id: 2 } })
          if (updateHrmcFormDetail.length > 0) {
            /** update change request status */
            await ChangeRequest.update({ change_request_status: change_request_status }, { where: { id: findRequestExist.id } })
            /** create change request history */
            await ChangeRequestHistory.create({ change_request_id: findRequestExist.id, change_request_subject: findRequestExist.change_request_subject, old_data: findRequestExist.old_data, new_data: findRequestExist.new_data, change_request_status: change_request_status, change_request_history_status: change_request_history_status.ACTIVE, user_id: req.user.id, created_by: req.user.id, updated_by: req.user.id, change_request_files: findRequestExist.change_request_files, change_request_remark: change_request_remark } as any)
          }

          if (newData.medical_disability || newData.medical_disability_detail || newData.kin1_name || newData.kin1_relation || newData.kin1_address || newData.kin1_mobile_number || newData.kin2_name || newData.kin2_relation || newData.kin2_address || newData.kin2_mobile_number || newData.professional1_name_contact || newData.professional1_role_description || newData.professional1_start_date || newData.professional1_end_date || newData.professional2_name_contact || newData.professional2_role_description || newData.professional2_start_date || newData.professional2_end_date || newData.passport_no || newData.issued_date || newData.permit_type || newData.permit_type_other || newData.validity || newData.bank_account_name || newData.bank_account_number || newData.bank_sort_code || newData.bank_society_name || newData.bank_address || newData.has_student_or_pg_loan || newData.has_p45_form || newData.hmrc_p45_form) {
            const updateStarterFormDetail = await StarterForm.update(newData, { where: { user_id: findRequestExist.user_id, status: status.ACTIVE, checklist_id: 2 } })
            if (updateStarterFormDetail.length > 0) {
              /** update change request status */
              await ChangeRequest.update({ change_request_status: change_request_status }, { where: { id: findRequestExist.id } })
            }
          }
        } else {
          const updateStarterFormDetail = await StarterForm.update(newData, { where: { user_id: findRequestExist.user_id, status: status.ACTIVE, checklist_id: 2 } })
          if (updateStarterFormDetail.length > 0) {
            /** update change request status */
            await ChangeRequest.update({ change_request_status: change_request_status }, { where: { id: findRequestExist.id } })
            /** create change request history */
            await ChangeRequestHistory.create({ change_request_id: findRequestExist.id, change_request_subject: findRequestExist.change_request_subject, old_data: findRequestExist.old_data, new_data: findRequestExist.new_data, change_request_status: change_request_status, change_request_history_status: change_request_history_status.ACTIVE, user_id: req.user.id, created_by: req.user.id, updated_by: req.user.id, change_request_files: findRequestExist.change_request_files, change_request_remark: change_request_remark } as any)
          }
        }
        return res.status(StatusCodes.OK).json({
          status: true,
          message: res.__("REQUEST_STATUS_UPDATED"),
        });
      } else {
        /** if status is rejected, closed or cancelled then  update change request and create change request history */
        const updateChangeRequest = await ChangeRequest.update({ change_request_status: change_request_status }, { where: { id: findRequestExist.id } })
        if (updateChangeRequest.length > 0) {
          await ChangeRequestHistory.create({ change_request_id: findRequestExist.id, change_request_subject: findRequestExist.change_request_subject, old_data: findRequestExist.old_data, new_data: findRequestExist.new_data, change_request_status: change_request_status, change_request_history_status: change_request_history_status.ACTIVE, user_id: req.user.id, created_by: req.user.id, updated_by: req.user.id, change_request_files: findRequestExist.change_request_files, change_request_remark: change_request_remark } as any)
          if (change_request_status == changeRequestStatus.REJECTED) {
            const getRequestUserDetail: any = await User.findOne({
              attributes: ['id', 'webAppToken', 'appToken'],
              where: { id: findRequestExist.user_id, organization_id: req.user.organization_id }, raw: true
            });
            const getFullName = await getUserFullName(req.user.id)
            await createNotification([getRequestUserDetail], req, NOTIFICATION_TYPE.INDIVIDUAL, NOTIFICATIONCONSTANT.CHANGE_REQUEST_REJECTED.content(getFullName), NOTIFICATIONCONSTANT.CHANGE_REQUEST_REJECTED.heading, REDIRECTION_TYPE.CHANGE_REQUEST, findRequestExist.id, { change_request_id: findRequestExist.id })
          }
          return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("REQUEST_STATUS_UPDATED"),
          });
        } else {
          return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("REQUEST_STATUS_UPDATED"),
          });
        }
      }
    } else {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("FAIL_CHANGE_REQUEST_NOT_FOUND"),
      });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const getChangeRequestHistoryById = async (req: any, res: any) => {
  try {
    const { change_request_id } = req.params
    let findRequestExist: any = await ChangeRequest.findOne({
      include: [
        {
          model: User,
          as: "change_request_user",
          attributes: [
            'id', [
              sequelize.fn(
                "concat",
                sequelize.col("user_first_name"),
                " ",
                sequelize.col("user_last_name"),
              ),
              "user_full_name",
            ],
            "user_email",
            "employment_number"
          ],
          where: {
            organization_id: req.user.organization_id
          },
          include: [
            {
              model: Branch,
              as: "branch",
              attributes: ["id", "branch_name"],
              where: {
                organization_id: req.user.organization_id
              }
            }
          ]
        }
      ],
      attributes: ['id', 'change_request_subject', 'change_request_status', 'createdAt'], where: { id: change_request_id }
    })
    findRequestExist = JSON.parse(JSON.stringify(findRequestExist))
    if (findRequestExist) {
      const findRequestHistory = await ChangeRequestHistory.findAll({
        attributes: [
          "id",
          "old_data",
          "new_data",
          "change_request_status",
          "change_request_files",
          "change_request_subject",
          "change_request_remark",
          "updatedAt",
        ],
        where: { change_request_id: findRequestExist.id },
      });

      const responseData = {
        ...findRequestExist,
        request_history: [],
        change_request_files: [],
        baseUrl: `${global.config.API_BASE_URL}`,
      };

      if (findRequestHistory.length > 0) {
        responseData.request_history = await Promise.all(
          findRequestHistory.map(async (requestHistory) => {
            const historyData = JSON.parse(JSON.stringify(requestHistory));

            // Process files for history records
            try {
              const historyFileIds = JSON.parse(
                historyData.change_request_files,
              );
              historyFileIds.map((ids: any) => Number(ids));
              if (historyFileIds && historyFileIds.length > 0) {
                const items = await Item.findAll({
                  where: { id: { [Op.in]: historyFileIds } },
                  attributes: ["id", "item_location"],
                });

                // Create a map of existing items
                const itemMap = new Map(items.map((item: any) => [item.id, item.item_location]));
                // Map each file ID to either its item location or fallback path
                historyData.change_request_files = historyFileIds.map((fileId: string | number) => {
                  return itemMap.has(Number(fileId))
                    ? `${itemMap.get(Number(fileId))}`
                    : `change_request/${fileId}`;
                });
              } else {
                historyData.change_request_files = [];
              }
            } catch (err) {
              historyData.change_request_files = [];
            }

            return historyData;
          }),
        );
      }

      // Process files for main request
      if (
        findRequestExist.change_request_files &&
        findRequestExist.change_request_files !== ""
      ) {
        try {
          const mainFileIds = JSON.parse(findRequestExist.change_request_files);
          mainFileIds.map((ids: any) => Number(ids));
          if (mainFileIds && mainFileIds.length > 0) {
            const items = await Item.findAll({
              where: { id: { [Op.in]: mainFileIds } },
              attributes: ["id", "item_location"],
            });

            // Create a map of existing items
            const itemMap = new Map(items.map((item: any) => [item.id, item.item_location]));

            // Map each file ID to either its item location or fallback path
            responseData.change_request_files = mainFileIds.map((fileId: string | number) => {
              return itemMap.has(Number(fileId))
                ? `${itemMap.get(Number(fileId))}`
                : `change_request/${fileId}`;
            });
          }
        } catch (err) {
          responseData.change_request_files = [];
        }
      }

      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("SUCCESS_FETCHED"),
        data: responseData,
      });
    } else {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("FAIL_DATA_NOT_FOUND"),
        data: {},
      });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};


const deleteChangeRequestById = async (req: any, res: any) => {
  try {
    const { change_request_id } = req.params
    const findRequestExist: any = await ChangeRequest.findOne({ attributes: ['id', 'change_request_files', 'user_id', 'change_request_subject', 'old_data', 'new_data'], where: { id: change_request_id }, raw: true })
    if (findRequestExist) {
      const deleteChangeRequest = await ChangeRequest.update({ change_request_status: changeRequestStatus.DELETED }, { where: { id: findRequestExist.id } })
      if (deleteChangeRequest) {
        await ChangeRequestHistory.create({ change_request_id: findRequestExist.id, change_request_subject: findRequestExist.change_request_subject, old_data: findRequestExist.old_data, new_data: findRequestExist.new_data, change_request_status: changeRequestStatus.DELETED, change_request_history_status: change_request_history_status.ACTIVE, user_id: req.user.id, created_by: req.user.id, updated_by: req.user.id, change_request_files: findRequestExist.change_request_files } as any)
        const getRequestUserDetail: any = await User.findOne({
          attributes: ['id', 'webAppToken', 'appToken'],
          where: { id: findRequestExist.user_id, organization_id: req.user.organization_id }, raw: true
        });
        const getFullName = await getUserFullName(req.user.id)
        await createNotification([getRequestUserDetail], req, NOTIFICATION_TYPE.INDIVIDUAL, NOTIFICATIONCONSTANT.CHANGE_REQUEST_DELETED.content(getFullName), NOTIFICATIONCONSTANT.CHANGE_REQUEST_DELETED.heading, REDIRECTION_TYPE.CHANGE_REQUEST, null, null)
        return res.status(StatusCodes.OK).json({
          status: true,
          message: res.__("CHANGE_REQUEST_DELETED"),
        });
      } else {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: res.__("FAIL_CHANGE_REQUEST_DELETED"),
        });
      }
    } else {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("FAIL_CHANGE_REQUEST_NOT_FOUND"),
      });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};


const getChangeRequestFields = async (req: any, res: any) => {
  try {
    const includeFields = [
      'username', 'user_first_name', 'user_middle_name', 'user_last_name', 'user_email', 'date_of_birth', 'user_phone_number', 'emergency_contact', 'user_joining_date', 'country', 'user_gender', 'user_gender_other', 'marital_status', 'marital_status_other', 'has_right_to_work_in_uk', 'is_uk_citizen', 'has_student_or_pg_loan', 'passport_no', 'permit_type', 'validity', 'issued_date', 'insurance_number', 'bank_account_name', 'bank_account_number', 'bank_sort_code', 'bank_society_name', 'bank_address', 'kin1_name', 'kin1_relation', 'kin2_mobile_number', 'kin1_address', 'kin2_name', 'kin2_relation', 'kin2_address', 'medical_disability', 'medical_disability_detail', 'professional1_name_contact', 'professional1_role_description', 'professional1_start_date', 'professional1_end_date', 'professional2_name_contact', 'professional2_role_description', 'professional2_start_date', 'another_job', 'private_pension', 'statementA', 'statementB', 'statementC', 'postgraduate_loan', 'statement_apply', 'is_current_information', 'user_signature', 'user_avatar', 'brp_back', 'brp_front', 'ni_letter', 'p45', 'photoID', 'student_letter', 'statements_dl_utility', 'share_code', 'passport_front', 'passport_back', 'cv', 'hmrc_p45_form'
    ]; // Add more as needed

    const getFilteredAttributes = (Model: any) => {
      const modelAttrs = Object.keys(Model.getAttributes());
      const modelAttrSet = new Set(modelAttrs);

      return includeFields
        .filter(field => modelAttrSet.has(field))
        .map(key => ({
          key,
          label: key
            .replace(/_/g, " ")
            .replace(/\b\w/g, char => char.toUpperCase())
        }));
    }

    const filteredUserAttributes = getFilteredAttributes(User);
    const filterRightToWorkAttributes = getFilteredAttributes(RightToWorkCheckList);
    const filteredStarterFormAttributes = getFilteredAttributes(StarterForm);
    const filteredHrmcFormAttributes = getFilteredAttributes(HrmcForm);

    // Merge all arrays into one, excluding the specified fields
    const mergedAttributes = [
      ...filteredUserAttributes
    ];

    const merged3Attributes = [
      ...filteredStarterFormAttributes,
      ...filteredHrmcFormAttributes,
    ];

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_FETCHED"),
      data: {
        personal_details: mergedAttributes,
        right_to_work: filterRightToWorkAttributes,
        starter_form: merged3Attributes
      }
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const storeChangeRequestSettings = async (req: any, res: any) => {
  try {
    const { user_field_order } = req.body;

    /** check if user field order already exists */
    const userFieldOrder = await ChangeRequestSettings.findOne({
      where: { organization_id: req.user.organization_id }, raw: true
    });

    let userFieldSequence: any

    if (userFieldOrder) {
      userFieldSequence = await ChangeRequestSettings.update({
        key: JSON.stringify(user_field_order),
        updated_by: req.user.id,
      }, { where: { organization_id: req.user.organization_id } });
    } else {
      userFieldSequence = await ChangeRequestSettings.create({
        key: JSON.stringify(user_field_order),
        organization_id: req.user.organization_id,
        created_by: req.user.id,
        updated_by: req.user.id,
      } as any);
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_DATA_UPDATED"),
      data: userFieldSequence
    });

  } catch (error) {
    console.log(error)
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const getStoredChangeRequestField = async (req: any, res: any) => {
  try {
    const getUserFieldOrder: any = await ChangeRequestSettings.findOne({ where: { organization_id: req.user.organization_id }, raw: true });

    if (getUserFieldOrder) {
      // Parse the user_field_order JSON string
      getUserFieldOrder.key = JSON.parse(getUserFieldOrder.key);
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_DATA_FETCHED"),
      data: getUserFieldOrder,
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
}
export default {
  sendChangeRequest,
  getAllChangeRequest,
  updateRejectedRequest,
  getChangeRequestById,
  getOwnChangeRequestList,
  approveRejectChangeRequest,
  getChangeRequestHistoryById,
  deleteChangeRequestById,
  getChangeRequestFields,
  storeChangeRequestSettings,
  getStoredChangeRequestField
};