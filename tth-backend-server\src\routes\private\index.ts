import express, { Router } from "express";
import userController from "../../controller/user.controller";
import branchRoute from "./branch.routes";
import departmentRoute from "./department.routes";
import userRoute from "./user.routes";
import onbordingRoute from "./onbording.routes";
import mediaRoute from "./media.routes";
import requestRoute from "./request.routes";
import notificationRoute from "./notification.routes";
import settingRoute from "./setting.routes";
import resignationRoute from "./resignation.routes";
import dashboardRoute from "./dashboard.routes";
import cardRoute from "./card.routes";
import bankRoute from "./bank.routes";
import dsrRoute from "./dsr.routes";
import changRequestRoute from "./changeRequest.routes";
import userVerificationRoute from "./userVerification.routes";
import categoryRoute from "./category.routes";
import empContractRoute from "./contract.routes";
import expenseRoute from "./expense.routes";
import reportRoute from "./report.routes";
import forecastRoute from "./forecast.routes";
import holidayRoute from "./holiday.routes";
import leaveRoute from "./leavePolicy.routes";
import policySettingRoute from "./policySetting.routes";
import bannerRoute from "./bannerNotification.routes";
import sideLetterConfirmationRoute from "./sideLetterConfirmation.routes";
import rolesRoutes from "./roles.routes";

const routes: Router = express.Router();

routes.get("/refresh", userController.refreshToken);

routes.use("/branch", branchRoute);

routes.use("/department", departmentRoute);

routes.use("/user", userRoute);

routes.use("/onbording", onbordingRoute);

routes.use("/media", mediaRoute);

routes.use("/request", requestRoute);

routes.use("/notification", notificationRoute);

routes.use("/setting", settingRoute);

routes.use("/resignation", resignationRoute);

routes.use("/dashboard", dashboardRoute);

routes.use("/card", cardRoute);

routes.use("/bank", bankRoute)

routes.use("/dsr", dsrRoute)

routes.use("/changeRequest", changRequestRoute);

routes.use("/userVerification", userVerificationRoute);

routes.use("/category", categoryRoute);

routes.use("/contract", empContractRoute);

routes.use("/expense", expenseRoute)

routes.use("/report", reportRoute)

routes.use("/forecast", forecastRoute)

routes.use("/holiday", holidayRoute)

routes.use("/leavePolicy", leaveRoute)

routes.use("/policySetting", policySettingRoute)

routes.use("/banner", bannerRoute)
routes.use("/sideLetter", sideLetterConfirmationRoute)

routes.use("/role", rolesRoutes)

export default routes;
