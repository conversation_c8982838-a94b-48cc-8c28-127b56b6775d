import { Op } from "sequelize";
import { formatUserAgentData, generateEmploymentNumber, getOrganizationLogo, getOrgName, sendEmailNotification, sendInvitation } from "../helper/common";
import { encrypt } from "../helper/utils";
import { Role } from "../models/Role";
import { User, user_status } from "../models/User";
import { invitation_status, UserInvite } from "../models/UserInvite";
import { UserRole } from "../models/UserRole";


import { UserSession } from "../models/UserSession";
import { Activity } from "../models/Activity";
import { BannerNotification } from "../models/BannerNotification";
import { BannerConfig } from "../models/BannerConfig";
import { EMAIL_ADDRESS } from "../helper/constant";

/** Store staff details */
const staffUpdateDetails = async (staffData: any) => {
    try {
        staffData = staffData.staffResponse;

        /** check if staff action is reset password */
        if (staffData.type == 'reset_password') {
            const getUserData: any = await User.findOne({ where: { keycloak_auth_id: staffData.userId }, attributes: ['id', 'user_status'], raw: true })
            if (getUserData) {
                /** check user status, if it's pending then update with active status */
                await User.update({
                    user_status:
                        getUserData.user_status == user_status.PENDING
                            ? user_status.ACTIVE
                            : getUserData.user_status,
                }, { where: { id: getUserData.id } })
                const findUserInvitation = await UserInvite.findOne({ where: { user_id: getUserData.id, invitation_status: { [Op.not]: invitation_status.ACCEPTED } } })
                if (findUserInvitation) {
                    await UserInvite.update({ invitation_status: invitation_status.ACCEPTED, action_by: getUserData.id, updated_by: getUserData.id }, { where: { id: findUserInvitation.id } })
                }
            }
        } else {
            /** Update keycloak auth id after creation staff user. */
            if (staffData) {
                const username = staffData.username ? staffData.username : null
                await User.update({ keycloak_auth_id: staffData.keycloak_auth_id, username: username }, {
                    where: {
                        id: staffData.userId
                    }
                })
            }
        }
    } catch (e: any) {
        console.error('Error in forgotPasswordService:', e);
        return { status: false, message: e }
    }
}
/** Store org master details */
const orgMasterDetails = async (masterData: any) => {
    try {
        masterData = masterData.orgMasterData;
        const employmentNumber = await generateEmploymentNumber(masterData.organization_id)

        if (masterData) {
            /** Prepare User object and store data */
            const createObj: any = {
                user_first_name: masterData.firstName,
                user_last_name: masterData.lastName,
                user_email: masterData.email,
                user_phone_number: masterData.phoneNumber,
                user_status: 'pending',
                keycloak_auth_id: masterData.keycloak_auth_id,
                organization_id: masterData.organization_id,
                user_password: await encrypt(masterData.userPassword),
                employment_number: employmentNumber,
                user_active_role_id: 1,
                web_user_active_role_id: 1,
                username: masterData.username,
                user_designation: masterData.user_designation
            }
            const createUser = await User.create(createObj)

            /** Get Super admin role */
            const getSuperAdminRole: any = await Role.findOne({ where: { parent_role_id: null as unknown as number } });

            /** Not exist then throw error */
            if (!getSuperAdminRole) {
                return { status: false, message: 'Super Admin role not found' }
            }
            /** Prepare User role object and store data */
            const createUserRole: any = {
                user_id: createUser.id,
                role_id: getSuperAdminRole.id
            }
            await UserRole.create(createUserRole);
        }
    } catch (e: any) {
        console.error('Error in forgotPasswordService:', e);
        return { status: false, message: e }
    }
}
/** Update org master status from pending to verified  */
const updateOrgMasterStatus = async (masterData: any) => {
    try {
        masterData = masterData.orgMaster;

        /** check masterData exist then update status from pending to verified */
        if (masterData) {
            await User.update({ user_status: user_status.VERIFIED }, {
                where: {
                    keycloak_auth_id: masterData.keycloak_auth_id
                }
            })
        }
    } catch (e: any) {
        console.error('Error in forgotPasswordService:', e);
        return { status: false, message: e }
    }
}
/** staff creation mail */
const staffCreationMail = async (mailData: any) => {
    try {
        mailData = mailData.mailResponse;
        if (mailData) {
            /** check if consumer queue id for staff reinvitation then execute below code */
            if (mailData && mailData.staffData) {
                const staffData: any = mailData.staffData;
                if (staffData.email) {
                    const templateData = {
                        name: staffData.name,
                        email: staffData.email,
                        password: staffData.user_password,
                        role: staffData.role,
                        username: mailData.username,
                        mail_type: 'send_invitation',
                        ORGANIZATION_LOGO: await getOrganizationLogo(mailData.organization_id),
                        LOGO: global.config.API_UPLOAD_URL + "/email_logo/logo.png",
                        ADDRESS: EMAIL_ADDRESS.ADDRESS,
                        PHONE_NUMBER: EMAIL_ADDRESS.PHONE_NUMBER,
                        EMAIL: EMAIL_ADDRESS.EMAIL,
                        ORGANIZATION_NAME: EMAIL_ADDRESS.ORGANIZATION_NAME,
                        smtpConfig: 'INFO',
                        organization: await getOrgName(mailData.organization_id)
                    };
                    await sendEmailNotification(templateData)
                }
            } else {
                /** below code is first time invitation of staff */
                const addUserId = mailData.staffUserId
                const adminId = mailData.adminId
                const password = mailData.password
                const username = mailData.username
                /** Send mail */
                await sendInvitation([addUserId], adminId, username, password)
            }
        }
    } catch (e: any) {
        console.error('Error in forgotPasswordService:', e);
        return { status: false, message: e }
    }
}
/** session stored data */
const sessionStorage = async (loginData: any) => {
    try {
        const sessionData = loginData.sessionData
        const fetchUser: any = await User.findOne({ where: { keycloak_auth_id: sessionData.user_id }, attributes: ['id'], raw: true })
        if (fetchUser) {
            const userRoles: any = await UserRole.findAll({
                where: { user_id: fetchUser.id },
                include: [
                    {
                        model: Role,
                        as: "role",
                        attributes: ["id", "role_name"],
                    },
                ],
                nest: true,
                raw: true,
            });
            const roleData = userRoles[0]

            if (process.env.NEXT_NODE_ENV !== "staging" && roleData.role.name !== 'Super Admin') {
                await UserSession.destroy({ where: { user_id: fetchUser.id, device_type: sessionData.device_type } });
                // Store the new session in the database
                await UserSession.create({ user_id: fetchUser.id, device_type: sessionData.device_type, token: sessionData.token } as any);
            }
        }
    } catch (e: any) {
        console.error('Error in forgotPasswordService:', e);
        return { status: false, message: e }
    }
}

/** store activity log */
const storeActivityLog = async (task: any) => {
    try {
        const data = task.logData.message
        const headers = task.logData.header
        const { activity_table, activity_action, message, platformType } = data;
        /** let Get userData */
        const userData: any = await User.findOne({ where: { keycloak_auth_id: message.keycloak_userId }, attributes: ['id'], raw: true })

        await Activity.create({
            activity_table,
            activity_action,
            reference_id: userData?.id,
            ip_address: headers?.["ip-address"],
            address: headers?.["address"],
            userAgent: `${message?.platformType}` ? `${formatUserAgentData(headers?.["user-agent"], message?.platformType)}` : platformType,
            location: headers?.["location"],
            previous_data: JSON.stringify(data?._previousDataValues),
            new_data: JSON.stringify(message),
            organization_id: message.organization_id,
            created_by: userData?.id || null,
            updated_by: userData?.id || null,
        } as any)
    } catch (e: any) {
        console.error('Error in forgotPasswordService:', e);
        return { status: false, message: e }
    }
}

/** store Banner Notifications */
const storeBannerNotification = async (notificationData: any) => {
    try {
        const whereObj: any = {};
        /** check if plan status is trial_plan or renew_plan then set key as subscription */
        if (notificationData.plan_status == 'trial_plan' || notificationData.plan_status == 'renew_plan') {
            whereObj.key = 'subscription'
        }

        if (notificationData.plan_status == 'cancel_plan') {
            whereObj.key = 'subscription_cancel'
        }

        const bannerConfig: any = await BannerConfig.findOne({ where: whereObj, raw: true })
        let createObj: any = {}
        if (bannerConfig) {
            createObj.banner_config_id = bannerConfig.id
        }
        createObj = { ...createObj, ...notificationData }
        await BannerNotification.create(createObj as any)
    } catch (e: any) {
        console.error('Error in forgotPasswordService:', e);
        return { status: false, message: e }
    }
}

export { staffUpdateDetails, orgMasterDetails, updateOrgMasterStatus, staffCreationMail, sessionStorage, storeActivityLog, storeBannerNotification }