import { Request, Response } from "express";
import { Branch, branch_status } from "../models/Branch";
import { Op } from "sequelize";
import {  permittedForAdmin } from "../helper/common";
import { StatusCodes } from "http-status-codes";
import {  getPaginatedItems, getPagination } from "../helper/utils";
import { ROLE_CONSTANT } from "../helper/constant";
import { Card , card_status as cardStatus} from "../models/Card";
/**
 *  Create new  branch. Only admin/director/hr can create a new branch.
 * @param req
 * @param res
 * @returns
 */

const addCard = async (req: Request, res: Response) => {
  try {
    const { card_name = "", card_number = "", card_status, branch_id } = req.body;
    const checkPermission = await permittedForAdmin(req.user?.id, [
      ROLE_CONSTANT.SUPER_ADMIN,
      ROLE_CONSTANT.ADMIN,
      ROLE_CONSTANT.DIRECTOR,
      ROLE_CONSTANT.ACCOUNTANT,
    ]);
    if (!checkPermission)
      return res
        .status(StatusCodes.FORBIDDEN)
        .json({ status: false, message: res.__("PERMISSION_DENIED") });

    const findBranch = await Branch.findOne({where:{id : branch_id, branch_status : {[Op.not] :branch_status.DELETED}}})   
    if(!findBranch){
      return res
      .status(StatusCodes.BAD_REQUEST)
      .json({ status: false, message: res.__("BRANCH_NOT_FOUND") });
    }
    const findCard = await Card.findOne({
      where: {
        card_number : card_number,
        card_status: { [Op.not]: cardStatus.DELETED },
        branch_id : findBranch.id
      },
    });
    
    if (findCard) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("CARD_ALREADY_EXIST") });
    } else {
      const findCardByBranch = await Card.findOne({
        where: {
          card_status: { [Op.not]: cardStatus.DELETED },
          branch_id : findBranch.id
        },order :[['card_order','DESC']]
      });
      const addCard: any = await Card.setHeaders(req).create({
        card_name,
        card_number :card_number,
        card_status: card_status,
        branch_id,
        card_order :findCardByBranch ?  findCardByBranch.card_order  + 1 :  1,
        created_by: req.user.id,
        updated_by: req.user.id,
      } as any);
      if (addCard) {
        return res.status(StatusCodes.CREATED).json({
          status: true,
          message: res.__("SUCCESS_CARD_ADDED"),
          data: addCard,
        });
      } else {
        return res
          .status(StatusCodes.BAD_REQUEST)
          .json({ status: false, message: res.__("FAIL_CARD_ADDING") });
      }
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Update card
 * @param req
 * @param res
 * @returns
 */

const updateCard = async (req: Request, res: Response) => {
  try {
    const { card_name = "", card_number = "", card_status  } = req.body;
    const { card_id } = req.params;
    const checkPermission = await permittedForAdmin(req.user?.id, [
      ROLE_CONSTANT.SUPER_ADMIN,
      ROLE_CONSTANT.ADMIN,
      ROLE_CONSTANT.DIRECTOR,
      ROLE_CONSTANT.ACCOUNTANT,
    ]);
    if (!checkPermission)
      return res
        .status(StatusCodes.FORBIDDEN)
        .json({ status: false, message: res.__("PERMISSION_DENIED") });
    const findCard = await Card.findOne({
      where: {
        id : card_id,
        card_status: { [Op.not]: cardStatus.DELETED }
      },
    });
    if (!findCard) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("CARD_NOT_FOUND") });
    } else {
      const updateCard: any = await Card.setHeaders(req).update({
        card_name,
        card_number,
        card_status: card_status,
        card_order : findCard.card_order,
        branch_id : findCard.branch_id,
        updated_by: req.user.id,
      },{where:{id : findCard.id}});
      if (updateCard.length > 0) {
        return res.status(StatusCodes.CREATED).json({
          status: true,
          message: res.__("SUCCESS_CARD_UPDATED")
        });
      } else {
        return res
          .status(StatusCodes.BAD_REQUEST)
          .json({ status: false, message: res.__("FAIL_CARD_UPDATING") });
      }
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Get All branch list
 * @param req
 * @param res
 * @returns
 */

const getAllCard = async (req: Request, res: Response) => {
  try {
     const {page ,size,card_status ,search }: any = req.query
     const { limit, offset } = getPagination(page, size);
      const cardObj : any = {
        where :{card_status :{[Op.not] : cardStatus.DELETED} }
      }
      if(search){
        cardObj.where.card_name = { [Op.like]: `%${search}%` }
      }
      if (page && size) {
        cardObj.limit = Number(limit);
        cardObj.offset = Number(offset);
      }
      if(card_status){
        cardObj.where.card_status = card_status
      }
      const { count ,rows: cardList} = await Card.setHeaders(req).findAndCountAll(cardObj);
      const { total_pages } = getPaginatedItems(
        size,
        page,
        count || 0,
      );
      return res.status(StatusCodes.OK).json({
        status: true,
        data: cardList,
        message: res.__("SUCCESS_FETCHED"),
        count: count,
        page: parseInt(page),
        size: parseInt(size),
        total_pages,
      });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Delete  by id
 * @param req
 * @param res
 * @returns
 */

const deleteCard = async (req: Request, res: Response) => {
  try {
    const { card_id } = req.params;
    const getCard = await Card.findOne({ where:{id : card_id , card_status : {[Op.not]: cardStatus.DELETED}}});
    if (!getCard) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("CARD_NOT_FOUND")
      });
    } 
    const deleteCard = await Card.update({ card_status: cardStatus.DELETED }, { where: {id: getCard.id}})
    if(deleteCard.length > 0){
      return res.status(StatusCodes.OK).json({status : true , message : res.__("SUCCESS_CARD_DELETED")})
    }else{
      return res.status(StatusCodes.BAD_REQUEST).json({status : false , message : res.__("FAIL_CARD_DELETING")})
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Get card by id
 * @param req
 * @param res
 * @returns
 */

const getCardById = async (req: Request, res: Response) => {
  try {
    const { card_id } = req.params;
    const getCard = await Card.findOne({ where:{id : card_id , card_status : {[Op.not]: cardStatus.DELETED}} });
    if (!getCard) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        data: {},
        message: res.__("CARD_NOT_FOUND")
      });
    }else{
      return res.status(StatusCodes.OK).json({
        status: true,
        data: getCard || {},
        message: res.__("SUCCESS_FETCHED")
      });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const getCardByBranch = async (req: Request, res: Response) => {
  try {
    const { branch_id } = req.params;
    const {page ,size,card_status ,search }: any = req.query
    const { limit, offset } = getPagination(page, size);
    const findBranch = await Branch.findOne({where:{id : branch_id, branch_status : {[Op.not] :branch_status.DELETED}}})   
    if(!findBranch){
        return res
        .status(StatusCodes.BAD_REQUEST)
        .json({ status: false, message: res.__("BRANCH_NOT_FOUND") });
    }
    const getCardObj : any = { 
      where: {card_status : {[Op.not]: cardStatus.DELETED}, branch_id  :branch_id},
      order : [['card_order','ASC']]
    }
    if(page && size){
      getCardObj.limit = Number(limit)
      getCardObj.offset = Number(offset)
    }
    if(card_status){
      getCardObj.where.card_status = card_status
    }
    if(search){
      getCardObj.where.card_name = { [Op.like]: `%${search}%` }
    }
    const { count,rows :getCardByBranch} = await Card.findAndCountAll(getCardObj);
    const { total_pages } = getPaginatedItems(
      size,
      page,
      count || 0,
    );
    return res.status(StatusCodes.OK).json({
      status: true,
      data: getCardByBranch,
      message: res.__("SUCCESS_FETCHED"),
      count: count,
      page: parseInt(page),
      size: parseInt(size),
      total_pages,
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const updateCardOrder = async (req: any, res: any) => {
  try {
    const { card_list } = req.body;
    const { branch_id } = req.params;

    const findBranch = await Branch.findOne({
      where: {
        id: branch_id,
        branch_status: { [Op.not]: branch_status.DELETED },
      },
    });
    if (!findBranch) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("BRANCH_NOT_FOUND") });
    }

    for (let i = 0; card_list.length > i; i++) {
      const findCard = await Card.findOne({
        where: { id: card_list[i] },
      });

      if (findCard) {
        await Card.setHeaders(req).update(
          {
            card_order : i + 1,
            updated_by: req.user.id,
          },
          {
            where: {
              branch_id: branch_id,
              id : card_list[i],
            },
          },
        );
      }
    }
    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_CARD_ORDER_UPDATED"),
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

export default {
  addCard,
  updateCard,
  getAllCard,
  deleteCard,
  getCardById,
  getCardByBranch,
  updateCardOrder
};
