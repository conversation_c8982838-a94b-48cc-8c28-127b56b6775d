# Onboarding API Fixes and Optimizations

## Issues Identified and Fixed

### 1. Duplicate s3Result Console Logs and Database Entries

**Problem**: The `generateEmployeeContract` function was using `readHTMLFile` with a callback but being awaited incorrectly, causing race conditions and duplicate database entries.

**Root Cause**: 
- The function returned immediately without waiting for PDF generation to complete
- No proper Promise handling for the callback-based `readHTMLFile` function
- Multiple database operations without transaction control

**Solution Applied**:
- Wrapped `generateEmployeeContract` in a proper Promise to ensure synchronous execution
- Added database transaction control to prevent duplicate entries
- Removed the problematic `setTimeout` that was causing response delays

### 2. Checklist API Not Returning Records for checklist_id=4

**Problem**: The `getUserFormDetail` API was not returning records for checklist_id=4 (Employment Contract).

**Root Cause**: 
- Incorrect status enum being used in the query (`status.ACTIVE` instead of `contract_status.ACTIVE`)
- Missing proper ordering to get the latest contract

**Solution Applied**:
- Fixed the status enum to use `contract_status.ACTIVE`
- Added proper ordering to get the most recent contract
- Added comprehensive logging for debugging

### 3. Performance Issues

**Problem**: Unnecessary retry logic with 10 attempts and 1-second delays was causing API slowdowns.

**Root Cause**: 
- Overly complex retry mechanism for file reading
- Synchronous file operations that didn't need retries

**Solution Applied**:
- Removed unnecessary retry logic
- Simplified file reading to direct synchronous operation
- Added proper error handling

## Code Changes Made

### 1. Fixed generateEmployeeContract Function
```typescript
// Before: Callback-based function that didn't properly await
const generateEmployeeContract = async (destinationPath, data, template) => {
  readHTMLFile(path, async function (err, html) {
    // PDF generation code
  });
};

// After: Promise-based function with proper await
const generateEmployeeContract = async (destinationPath, data, template): Promise<void> => {
  return new Promise((resolve, reject) => {
    readHTMLFile(path, async function (err, html) {
      try {
        if (err) {
          reject(err);
          return;
        }
        // PDF generation code
        resolve();
      } catch (e) {
        reject(e);
      }
    });
  });
};
```

### 2. Added Database Transaction Control
```typescript
// Added transaction wrapper for PDF generation and database operations
const transaction = await sequelize.transaction();
try {
  const s3Result = await generateS3EmployeeContract(/* params */);
  if (s3Result.success) {
    // All database operations within transaction
    await User.setHeaders(req).update(/* params */, { transaction });
    await UserEmploymentContract.create(/* params */, { transaction });
    await transaction.commit();
  } else {
    await transaction.rollback();
  }
} catch (error) {
  await transaction.rollback();
  throw error;
}
```

### 3. Fixed getUserFormDetail Query
```typescript
// Before: Wrong status enum
const getContract = await UserEmploymentContract.findOne({
  where: { user_id: user_id, contract_status: status.ACTIVE },
});

// After: Correct status enum with ordering
const getContract = await UserEmploymentContract.findOne({
  where: { user_id: user_id, contract_status: contract_status.ACTIVE },
  order: [["id", "desc"]]
});
```

### 4. Removed Problematic setTimeout
```typescript
// Before: Caused response delays and potential duplicate responses
setTimeout(() => {
  return res.status(StatusCodes.OK).json(responseObj);
}, 2000);

// After: Direct response
return res.status(StatusCodes.OK).json(responseObj);
```

## Performance Improvements

1. **Reduced API Response Time**: Removed unnecessary 2-second delay
2. **Eliminated Retry Logic**: Simplified file operations
3. **Added Transaction Control**: Prevents duplicate database entries
4. **Better Error Handling**: More informative error messages

## Testing Recommendations

1. **Test Employment Contract Generation**:
   - Create a new employment contract
   - Verify only one database entry is created
   - Check that the PDF is generated correctly

2. **Test getUserFormDetail API**:
   - Call with checklist_id=4
   - Verify it returns the employment contract data
   - Check that the contract URL is accessible

3. **Test Concurrent Requests**:
   - Make multiple simultaneous requests to createForm API
   - Verify no duplicate entries are created

## Latest Changes (Today's Update)

### 4. Enhanced Duplicate Prevention with Locking Mechanism

**Problem**: Even with database transactions, duplicate entries were still being created due to concurrent requests.

**Root Cause**:
- `isCreated` was being set to "true" at the beginning of the EC block
- Response was sent based on `isCreated` regardless of actual completion
- No protection against concurrent requests for the same user

**Solution Applied**:
- Added in-memory lock mechanism to prevent concurrent contract generation
- Moved `isCreated = "true"` to only after successful transaction commit
- Added proper error handling with lock cleanup
- Added TOO_MANY_REQUESTS response for concurrent requests

### New Code Structure:
```typescript
// In-memory lock to prevent duplicate contract generation
const contractGenerationLocks = new Set<string>();

// In createForm function:
if (isCheckListExist.prefix == "EC") {
  const lockKey = `contract_generation_${user_id}`;

  // Check if already in progress
  if (contractGenerationLocks.has(lockKey)) {
    return res.status(StatusCodes.TOO_MANY_REQUESTS).json({
      status: false,
      message: "Contract generation already in progress. Please wait.",
    });
  }

  // Add lock
  contractGenerationLocks.add(lockKey);

  try {
    // PDF generation and database operations
    const transaction = await sequelize.transaction();
    try {
      // All operations within transaction
      await transaction.commit();
      isCreated = "true"; // Only set after success
    } catch (error) {
      await transaction.rollback();
      throw error;
    } finally {
      contractGenerationLocks.delete(lockKey); // Always cleanup
    }
  } catch (error) {
    contractGenerationLocks.delete(lockKey); // Cleanup on error
    return error response;
  }
}
```

## Complete Solution Summary

The final solution now includes:

1. **Promise-based PDF generation** (prevents async issues)
2. **Database transaction control** (prevents partial updates)
3. **In-memory locking mechanism** (prevents concurrent processing)
4. **Proper response timing** (only after successful completion)
5. **Comprehensive error handling** (with proper cleanup)

## Additional Recommendations

1. **Add API Rate Limiting**: Prevent abuse and reduce server load
2. **Implement Caching**: Cache frequently accessed data like settings
3. **Add Request Validation**: Validate all input parameters
4. **Monitor Database Performance**: Add query performance monitoring
5. **Add Comprehensive Logging**: Log all critical operations for debugging
6. **Consider Redis for Distributed Locking**: If running multiple server instances

## Database Schema Considerations

The current implementation assumes:
- `checklist_id=4` corresponds to Employment Contract (prefix="EC")
- `contract_status` enum values: "active", "inactive", "deleted"
- Proper foreign key relationships between tables

Verify these assumptions in your database schema.

## Testing the Fix

1. **Test single request**: Verify normal flow works
2. **Test concurrent requests**: Make multiple simultaneous requests
3. **Test error scenarios**: Verify proper cleanup on failures
4. **Monitor logs**: Check for "Contract generation already in progress" messages
