"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { Role } from "./Role";
import { Branch } from "./Branch";
import { Department } from "./Department";
import { addActivity } from "../helper/queue.service";
import { Activity } from "./Activity";
import { UserRole } from "./UserRole";
import { UserRequest } from "./UserRequest";
import { UserLeavingCheckList } from "./UserLeavingCheckList";
import { UserEmploymentContract } from "./UserEmployementContract";
import { UserCheckList } from "./UserCheckList";
import { ResignationRemark } from "./ResignationRemark";
import { Resignation } from "./Resignation";
import { PlaylistMediaTrack } from "./PlaylistMediaTrack";
import { DsrRequest } from "./DsrRequest";
import { DsrDetail } from "./DsrDetail";
import { ChangeRequestHistory } from "./ChangeRequestHistory";
import { ChangeRequest } from "./ChangeRequest";
import { UserInvite } from "./UserInvite";
import { UserMeta } from "./UserMeta";
import { WsrRequest } from "./WsrRequest";
import { WsrDetail } from "./WsrDetail";
import { ExpenseRequest } from "./ExpenseRequest";
import { ExpenseDetail } from "./ExpenseDetail";
import { ForecastHistory } from "./ForecastHistory";
import { UserWeekDay } from "./UserWeekDay";
import { LeaveAccuralPolicy } from "./LeaveAccuralPolicy";
import { LeaveApprovalMetaPolicy } from "./LeaveApprovalMetaPolicy";
import { Dashboard } from "./Dashboard";

interface userAttributes {
  id: number;
  user_first_name: string;
  user_middle_name: string;
  user_last_name: string;
  user_gender: string;
  user_gender_other: string;
  marital_status: string;
  marital_status_other: string;
  user_email: string;
  address_line1: string;
  address_line2: string;
  country: string;
  pin_code: number;
  user_phone_number: string;
  user_password: string;
  emergency_contact: string;
  user_designation: string;
  appToken: string;
  webAppToken: string;
  branch_id: number;
  department_id: number;
  date_of_birth: string;
  user_avatar: string;
  otp: number;
  otp_expire: string;
  user_status: string;
  user_login_pin: string;
  is_login_pin: boolean;
  user_joining_date: string;
  created_by: number;
  updated_by: number;
  user_signature: string;
  user_active_role_id: number;
  web_user_active_role_id: number;
  confirm_by: number;
  confirm_by_date: string;
  createdAt?: string;
  employment_contract: string;
  token_version: number;
  user_verification_doc: string;
  last_reject_remark: string;
  pin_token_version: number;
  geo_country: string;
  geo_state: string;
  geo_city: string;
  employment_number: string;
  organization_id: string;
  keycloak_auth_id: string;
  username: string;
  rota_group_by: string;
  list_order: number;
}

/** User enum  for status*/
export enum user_status {
  PENDING = "pending",
  ACTIVE = "active",
  ONGOING = "ongoing",
  COMPLETED = "completed",
  VERIFIED = "verified",
  DELETED = "deleted",
  REJECTED = "rejected",
  CANCELLED = "cancelled",
}

export enum gender {
  MALE = "male",
  FEMALE = "female",
}

export enum marital_status {
  MARRIED = "married",
  SINGLE = "single",
}

export class User
  extends Model<userAttributes, never>
  implements userAttributes {
  id!: number;
  user_first_name!: string;
  user_middle_name!: string;
  user_last_name!: string;
  user_email!: string;
  user_gender!: string;
  user_gender_other!: string;
  marital_status!: string;
  marital_status_other!: string;
  address_line1!: string;
  address_line2!: string;
  country!: string;
  pin_code!: number;
  user_phone_number!: string;
  emergency_contact!: string;
  user_password!: string;
  user_designation!: string;
  branch_id!: number;
  department_id!: number;
  user_avatar!: string;
  date_of_birth!: string;
  otp!: number;
  appToken!: string;
  webAppToken!: string;
  confirm_by!: number;
  confirm_by_date!: string;
  otp_expire!: string;
  user_status!: string;
  user_login_pin!: string;
  user_joining_date!: string;
  user_signature!: string;
  created_by!: number;
  updated_by!: number;
  is_login_pin!: boolean;
  user_active_role_id!: number;
  web_user_active_role_id!: number;
  employment_contract!: string;
  token_version!: number;
  user_verification_doc!: string;
  last_reject_remark!: string;
  pin_token_version!: number
  geo_country!: string;
  geo_state!: string;
  geo_city!: string;
  employment_number!: string;
  organization_id!: string;
  keycloak_auth_id!: string;
  username!: string;
  rota_group_by!: string;
  list_order!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

User.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    user_first_name: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    user_middle_name: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    user_last_name: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    user_email: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    address_line1: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    address_line2: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    country: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    pin_code: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    user_phone_number: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    emergency_contact: {
      type: DataTypes.STRING,
    },
    user_password: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    user_designation: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    user_signature: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    branch_id: {
      type: DataTypes.INTEGER,
    },
    otp: {
      type: DataTypes.INTEGER,
    },
    department_id: {
      type: DataTypes.INTEGER,
    },
    user_avatar: {
      type: DataTypes.STRING,
    },
    appToken: {
      type: DataTypes.STRING,
    },
    webAppToken: {
      type: DataTypes.STRING,
    },
    otp_expire: {
      type: DataTypes.DATE,
    },
    is_login_pin: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    user_status: {
      type: DataTypes.ENUM,
      values: Object.values(user_status),
      defaultValue: user_status.PENDING,
    },
    user_gender: {
      type: DataTypes.ENUM,
      values: Object.values(gender),
      allowNull: true,
    },
    user_gender_other: {
      type: DataTypes.STRING,
    },
    marital_status: {
      type: DataTypes.ENUM,
      values: Object.values(marital_status),
      allowNull: true,
    },
    marital_status_other: {
      type: DataTypes.STRING,
    },
    user_login_pin: {
      type: DataTypes.STRING,
    },
    user_joining_date: {
      type: DataTypes.DATE,
    },
    date_of_birth: {
      type: DataTypes.DATE,
    },
    confirm_by: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    confirm_by_date: {
      type: DataTypes.DATE,
      allowNull: true
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
    user_active_role_id: {
      type: DataTypes.INTEGER,
    },
    web_user_active_role_id: {
      type: DataTypes.INTEGER,
    },
    employment_contract: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    token_version: {
      type: DataTypes.BOOLEAN,
      defaultValue: 0
    },
    user_verification_doc: {
      type: DataTypes.STRING,
    },
    last_reject_remark: {
      type: DataTypes.STRING,
    },
    pin_token_version: {
      type: DataTypes.BOOLEAN,
      defaultValue: 0
    },
    geo_country: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    geo_state: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    geo_city: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    employment_number: {
      type: DataTypes.STRING,
      allowNull: false
    },
    keycloak_auth_id: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    organization_id: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    username: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    rota_group_by: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    list_order: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: null,
      set(value: number) {
        if (!value && this.getDataValue('id')) {
          this.setDataValue('list_order', this.getDataValue('id'));
        } else {
          this.setDataValue('list_order', value);
        }
      }
    }
  },
  {
    sequelize: sequelize,
    tableName: "nv_users",
    modelName: "User",
    timestamps: true,
  },
);

// Role.hasOne(User, { foreignKey: "user_active_role_id", as: "Role" });
User.belongsTo(Role, { foreignKey: "user_active_role_id", as: "role" });
Role.hasMany(User, { foreignKey: "user_active_role_id", as: "users" });
User.belongsTo(Branch, { foreignKey: "branch_id", as: "branch" });
Branch.hasMany(User, { foreignKey: "branch_id", as: "users" });
User.belongsTo(Department, { foreignKey: "department_id", as: "department" });
Department.hasMany(User, { foreignKey: "department_id", as: "users" });
User.hasMany(Activity, { foreignKey: "created_by", as: "users" });
Activity.belongsTo(User, { foreignKey: "created_by", as: "users" });
UserRole.belongsTo(User, { foreignKey: "user_id", as: "user" });
User.hasMany(UserRole, { foreignKey: "user_id", as: "user_roles" });
UserRequest.belongsTo(User, {
  foreignKey: "from_user_id",
  as: "request_from_users",
})
User.hasMany(UserRequest, {
  foreignKey: "from_user_id",
  as: "request_from_user",
})
UserRequest.belongsTo(User, {
  foreignKey: "request_approved_by",
  as: "request_approved_users",
});
User.hasMany(UserRequest, {
  foreignKey: "request_approved_by",
  as: "request_approved_user",
});
UserLeavingCheckList.belongsTo(User, {
  foreignKey: "to_user_id",
  as: "leaving_to_user_list",
});
User.hasMany(UserLeavingCheckList, { foreignKey: "to_user_id", as: "leaving_to_user" });

UserLeavingCheckList.belongsTo(User, {
  foreignKey: "from_user_id",
  as: "leaving_from_user_list",
});
User.hasMany(UserLeavingCheckList, { foreignKey: "from_user_id", as: "leaving_from_user" });
UserEmploymentContract.belongsTo(User, { foreignKey: "user_id", as: "user_employment_contract" });
User.hasMany(UserEmploymentContract, {
  foreignKey: "user_id",
  as: "user_contract",
});
UserCheckList.belongsTo(User, {
  foreignKey: "from_user_id",
  as: "from_user_list",
});
User.hasMany(UserCheckList, { foreignKey: "from_user_id", as: "from_user" });
UserCheckList.belongsTo(User, { foreignKey: "to_user_id", as: "to_user_list" });
User.hasMany(UserCheckList, { foreignKey: "to_user_id", as: "to_user_list" });
ResignationRemark.belongsTo(User, {
  foreignKey: "user_id",
  as: "resign_remark_user",
});
User.hasMany(ResignationRemark, {
  foreignKey: "user_id",
  as: "user_remark_detail",
});
Resignation.belongsTo(User, {
  foreignKey: "user_id",
  as: "resign_user",
});
User.hasMany(Resignation, {
  foreignKey: "user_id",
  as: "user_detail",
});

Resignation.belongsTo(User, {
  foreignKey: "created_by",
  as: "resignation_created_user",
});
User.hasMany(Resignation, {
  foreignKey: "created_by",
  as: "resignation_created_user_detail",
});

Resignation.belongsTo(User, {
  foreignKey: "updated_by",
  as: "resignation_updated_user",
});
User.hasMany(Resignation, {
  foreignKey: "updated_by",
  as: "resignation_updated_user_detail",
});
User.hasMany(PlaylistMediaTrack, { foreignKey: "user_id" });
PlaylistMediaTrack.belongsTo(User, { foreignKey: "user_id" });
User.hasMany(PlaylistMediaTrack, { foreignKey: "created_by" });
PlaylistMediaTrack.belongsTo(User, { foreignKey: "created_by" });
User.hasMany(PlaylistMediaTrack, { foreignKey: "updated_by" });
PlaylistMediaTrack.belongsTo(User, { foreignKey: "updated_by" });
DsrRequest.belongsTo(User, { foreignKey: "user_id", as: "dsr_request_user" });
User.hasMany(DsrRequest, { foreignKey: "user_id", as: "user_request" });
DsrRequest.belongsTo(User, { foreignKey: "created_by", as: "dsr_request_created_by" });
User.hasMany(DsrRequest, { foreignKey: "created_by" });
DsrRequest.belongsTo(User, { foreignKey: "updated_by", as: "dsr_request_updated_by" });
User.hasMany(DsrRequest, { foreignKey: "updated_by" });

DsrDetail.belongsTo(User, { foreignKey: "user_id", as: "dsr_user" });
User.hasMany(DsrDetail, { foreignKey: "user_id", as: "user" });
DsrDetail.belongsTo(User, { foreignKey: "created_by", as: "dsr_detail_created_by" });
User.hasMany(DsrDetail, { foreignKey: "created_by" });
DsrDetail.belongsTo(User, { foreignKey: "updated_by", as: "dsr_detail_updated_by" });
User.hasMany(DsrDetail, { foreignKey: "updated_by" });

ChangeRequestHistory.belongsTo(User, {
  foreignKey: "user_id",
  as: "change_request_history_user",
});
User.hasMany(ChangeRequestHistory, {
  foreignKey: "user_id",
  as: "change_request_user_history_detail",
});
ChangeRequest.belongsTo(User, {
  foreignKey: "user_id",
  as: "change_request_user",
});
User.hasMany(ChangeRequest, {
  foreignKey: "user_id",
  as: "change_request_user_detail",
});
UserInvite.belongsTo(User, { foreignKey: "user_id", as: "user_invite" });
User.hasOne(UserInvite, { foreignKey: "user_id", as: "invitation" });
UserInvite.belongsTo(User, { foreignKey: "action_by", as: "user_action" });
User.hasOne(UserInvite, { foreignKey: "action_by", as: "action" });

User.hasOne(UserMeta, {
  foreignKey: "user_id",
  as: "user_meta",
});
UserMeta.belongsTo(User, { foreignKey: "user_id", as: "user_meta" });

WsrRequest.belongsTo(User, { foreignKey: "user_id", as: "wsr_request_user" });
User.hasMany(WsrRequest, { foreignKey: "user_id", as: "wsr_user_request" });
WsrRequest.belongsTo(User, { foreignKey: "created_by", as: "wsr_request_created_by" });
User.hasMany(WsrRequest, { foreignKey: "created_by" });
WsrRequest.belongsTo(User, { foreignKey: "updated_by", as: "wsr_request_updated_by" });
User.hasMany(WsrRequest, { foreignKey: "updated_by" });

WsrDetail.belongsTo(User, { foreignKey: "user_id", as: "wsr_user" });
User.hasMany(WsrDetail, { foreignKey: "user_id", as: "wsr_user_detail" });
WsrDetail.belongsTo(User, { foreignKey: "created_by", as: "wsr_detail_created_by" });
User.hasMany(WsrDetail, { foreignKey: "created_by" });
WsrDetail.belongsTo(User, { foreignKey: "updated_by", as: "wsr_detail_updated_by" });
User.hasMany(WsrDetail, { foreignKey: "updated_by" });

ExpenseRequest.belongsTo(User, { foreignKey: "user_id", as: "expense_request_user" });
User.hasMany(ExpenseRequest, { foreignKey: "user_id", as: "expense_user_request" });
ExpenseRequest.belongsTo(User, { foreignKey: "created_by", as: "expense_request_created_by" });
User.hasMany(ExpenseRequest, { foreignKey: "created_by" });
ExpenseRequest.belongsTo(User, { foreignKey: "updated_by", as: "expense_request_updated_by" });
User.hasMany(ExpenseRequest, { foreignKey: "updated_by" });

ExpenseDetail.belongsTo(User, { foreignKey: "user_id", as: "expense_user" });
User.hasMany(ExpenseDetail, { foreignKey: "user_id", as: "expense_user_detail" });
ExpenseDetail.belongsTo(User, { foreignKey: "created_by", as: "expense_detail_created_by" });
User.hasMany(ExpenseDetail, { foreignKey: "created_by" });
ExpenseDetail.belongsTo(User, { foreignKey: "updated_by", as: "expense_detail_updated_by" });
User.hasMany(ExpenseDetail, { foreignKey: "updated_by" });

ForecastHistory.belongsTo(User, { foreignKey: "created_by", as: "forecast_history_created_by" });
User.hasMany(ForecastHistory, { foreignKey: "created_by" });

// regarding leave policy

LeaveAccuralPolicy.belongsTo(User, { foreignKey: "created_by", as: "leave_accural_policy_created_by" });
User.hasMany(LeaveAccuralPolicy, { foreignKey: "created_by" });
LeaveAccuralPolicy.belongsTo(User, { foreignKey: "updated_by", as: "leave_accural_policy_updated_by" });
User.hasMany(LeaveAccuralPolicy, { foreignKey: "updated_by" });
LeaveApprovalMetaPolicy.belongsTo(User, { foreignKey: "user_id", as: "leave_approval_meta_user_id" });
User.hasMany(LeaveApprovalMetaPolicy, { foreignKey: "user_id" });

User.hasOne(UserWeekDay, {
  foreignKey: "user_id",
  as: "user_week_day",
});
UserWeekDay.belongsTo(User, { foreignKey: "user_id", as: "week_day" });

Dashboard.belongsTo(User, { foreignKey: "user_id", as: "users" });
User.hasMany(Dashboard, { foreignKey: "user_id", as: "dashboards" });

User.addHook("afterUpdate", async (user: any) => {
  await addActivity("User", "updated", user);
});

User.addHook("afterCreate", async (user: User) => {
  if (!user.list_order) {
    await user.update({ list_order: user.id });
  }
  await addActivity("User", "created", user);
});

User.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});
